// Move App Pro 测试文件
//
// 这是一个全面的测试文件，验证应用的核心功能和中文注解
//
// 测试覆盖范围：
// - 应用启动和初始化
// - 用户认证功能
// - 路由导航系统
// - 路由守卫机制
// - 用户权限管理
//
// 运行测试命令：
// ```bash
// flutter test
// flutter test --coverage  # 生成覆盖率报告
// ```

import 'package:flutter_test/flutter_test.dart';
import 'package:move_app_pro/main.dart';
import 'package:move_app_pro/services/auth_service.dart';
import 'package:move_app_pro/utils/db_init.dart';
import 'package:move_app_pro/utils/db_util.dart';

void main() {
  group('Move App Pro 核心功能测试', () {

    /// 测试应用启动和初始化
    ///
    /// 验证内容：
    /// - 应用能够正常启动
    /// - 初始化过程正确执行
    /// - 首页能够正常显示
    /// - 基本UI元素存在
    testWidgets('应用启动测试', (WidgetTester tester) async {
      // 构建应用并触发一帧
      await tester.pumpWidget(const MyApp());

      // 等待所有异步操作完成
      await tester.pumpAndSettle();

      // 验证应用标题存在
      expect(find.text('Move App Pro'), findsOneWidget);

      // 验证欢迎信息存在
      expect(find.textContaining('欢迎'), findsWidgets);
    });

    /// 测试用户模型功能
    ///
    /// 验证内容：
    /// - 用户对象的创建和属性访问
    /// - JSON序列化和反序列化
    /// - 角色权限检查方法
    /// - 数据完整性验证
    test('用户模型测试', () {
      // 创建测试用户对象
      const user = User(
        id: '123',
        username: 'test_user',
        email: '<EMAIL>',
        roles: ['user', 'editor'],
      );

      // 验证用户基本属性
      expect(user.id, '123');
      expect(user.username, 'test_user');
      expect(user.email, '<EMAIL>');
      expect(user.roles, ['user', 'editor']);

      // 验证单个角色检查功能
      expect(user.hasRole('user'), true, reason: '用户应该拥有 user 角色');
      expect(user.hasRole('admin'), false, reason: '用户不应该拥有 admin 角色');

      // 验证多角色检查功能
      expect(user.hasAnyRole(['admin', 'editor']), true,
             reason: '用户拥有 editor 角色，应该通过检查');
      expect(user.hasAnyRole(['admin', 'moderator']), false,
             reason: '用户没有 admin 或 moderator 角色，不应该通过检查');

      // 验证JSON序列化功能
      final json = user.toJson();
      expect(json['id'], '123');
      expect(json['username'], 'test_user');
      expect(json['email'], '<EMAIL>');
      expect(json['roles'], ['user', 'editor']);

      // 验证JSON反序列化功能
      final userFromJson = User.fromJson(json);
      expect(userFromJson.id, user.id);
      expect(userFromJson.username, user.username);
      expect(userFromJson.email, user.email);
      expect(userFromJson.roles, user.roles);
    });
    test('数据库初始化与基础操作测试', () async {
      // 初始化数据库
      final db = await DBUtil.init();

      // 插入测试数据
      int id = await DBUtil.insert('users', {
        'username': 'test_user',
        'token': 'abc123',
      });

      expect(id, isNonZero, reason: '插入用户数据应返回有效 ID');

      // 查询用户
      final result = await DBUtil.query('users', where: 'username = ?', whereArgs: ['test_user']);

      expect(result, isNotEmpty, reason: '应能查询到刚刚插入的用户');
      expect(result.first['token'], 'abc123');
    });

  });
}
