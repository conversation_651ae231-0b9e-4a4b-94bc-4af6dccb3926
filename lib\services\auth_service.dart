import 'package:flutter/foundation.dart';

/// 用户信息模型类
///
/// 表示应用中的用户实体，包含用户的基本信息和角色权限
///
/// 功能特性：
/// - 不可变的用户数据模型
/// - 支持角色权限检查
/// - JSON序列化和反序列化
/// - 角色验证方法
///
/// 使用示例：
/// ```dart
/// // 创建用户对象
/// final user = User(
///   id: '123',
///   username: 'john_doe',
///   email: '<EMAIL>',
///   roles: ['user', 'editor'],
/// );
///
/// // 检查用户角色
/// if (user.hasRole('admin')) {
///   // 用户是管理员
/// }
///
/// // 检查多个角色
/// if (user.hasAnyRole(['editor', 'admin'])) {
///   // 用户是编辑者或管理员
/// }
/// ```
class User {
  /// 用户唯一标识符
  ///
  /// 通常是数据库中的主键或UUID
  /// 用于唯一标识用户身份
  final String id;

  /// 用户名
  ///
  /// 用户的登录名或显示名称
  /// 通常用于界面显示和登录验证
  final String username;

  /// 用户邮箱地址
  ///
  /// 用户的电子邮件地址
  /// 可用于登录验证、密码重置、通知发送等
  final String email;

  /// 用户角色列表
  ///
  /// 用户拥有的角色权限列表
  /// 用于访问控制和功能权限管理
  ///
  /// 常见角色：
  /// - 'user': 普通用户
  /// - 'admin': 管理员
  /// - 'editor': 编辑者
  /// - 'moderator': 版主
  /// - 'vip': VIP用户
  final List<String> roles;

  /// 创建用户对象
  ///
  /// 参数说明：
  /// - [id]: 用户ID，必填，不能为空
  /// - [username]: 用户名，必填，不能为空
  /// - [email]: 邮箱地址，必填，不能为空
  /// - [roles]: 角色列表，可选，默认为空列表
  ///
  /// 使用示例：
  /// ```dart
  /// // 创建普通用户
  /// final user = User(
  ///   id: '123',
  ///   username: 'john',
  ///   email: '<EMAIL>',
  /// );
  ///
  /// // 创建带角色的用户
  /// final admin = User(
  ///   id: '456',
  ///   username: 'admin',
  ///   email: '<EMAIL>',
  ///   roles: ['admin', 'user'],
  /// );
  /// ```
  const User({
    required this.id,
    required this.username,
    required this.email,
    this.roles = const [],
  });

  /// 从JSON数据创建用户对象
  ///
  /// 用于反序列化存储的用户数据或API响应
  ///
  /// 参数说明：
  /// - [json]: 包含用户信息的JSON映射
  ///   - 'id': 用户ID（必需）
  ///   - 'username': 用户名（必需）
  ///   - 'email': 邮箱地址（必需）
  ///   - 'roles': 角色列表（可选，默认为空）
  ///
  /// 返回值：
  /// - [User]: 创建的用户对象
  ///
  /// 使用示例：
  /// ```dart
  /// // 从API响应创建用户
  /// final jsonData = {
  ///   'id': '123',
  ///   'username': 'john',
  ///   'email': '<EMAIL>',
  ///   'roles': ['user', 'editor']
  /// };
  /// final user = User.fromJson(jsonData);
  ///
  /// // 从本地存储加载用户
  /// final storedJson = jsonDecode(prefs.getString('user_data'));
  /// final user = User.fromJson(storedJson);
  /// ```
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      roles: List<String>.from(json['roles'] ?? []),
    );
  }

  /// 将用户对象转换为JSON数据
  ///
  /// 用于序列化用户数据以便存储或网络传输
  ///
  /// 返回值：
  /// - [Map<String, dynamic>]: 包含用户信息的JSON映射
  ///
  /// 使用示例：
  /// ```dart
  /// final user = User(
  ///   id: '123',
  ///   username: 'john',
  ///   email: '<EMAIL>',
  ///   roles: ['user'],
  /// );
  ///
  /// // 保存到本地存储
  /// final jsonData = user.toJson();
  /// prefs.setString('user_data', jsonEncode(jsonData));
  ///
  /// // 发送到API
  /// final response = await http.post(
  ///   '/api/users',
  ///   body: jsonEncode(user.toJson()),
  /// );
  /// ```
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'roles': roles,
    };
  }

  /// 检查用户是否拥有指定角色
  ///
  /// 参数说明：
  /// - [role]: 要检查的角色名称
  ///
  /// 返回值：
  /// - [bool]: true表示用户拥有该角色，false表示没有
  ///
  /// 使用示例：
  /// ```dart
  /// final user = User(
  ///   id: '123',
  ///   username: 'john',
  ///   email: '<EMAIL>',
  ///   roles: ['user', 'editor'],
  /// );
  ///
  /// if (user.hasRole('admin')) {
  ///   // 显示管理员功能
  /// }
  ///
  /// if (user.hasRole('editor')) {
  ///   // 显示编辑功能
  /// }
  /// ```
  bool hasRole(String role) => roles.contains(role);

  /// 检查用户是否拥有指定角色列表中的任意一个角色
  ///
  /// 参数说明：
  /// - [requiredRoles]: 要检查的角色列表
  ///
  /// 返回值：
  /// - [bool]: true表示用户至少拥有其中一个角色，false表示都没有
  ///
  /// 使用示例：
  /// ```dart
  /// final user = User(
  ///   id: '123',
  ///   username: 'john',
  ///   email: '<EMAIL>',
  ///   roles: ['editor'],
  /// );
  ///
  /// // 检查是否有管理权限（管理员或编辑者）
  /// if (user.hasAnyRole(['admin', 'editor'])) {
  ///   // 显示管理功能
  /// }
  ///
  /// // 检查是否有高级权限
  /// if (user.hasAnyRole(['admin', 'moderator', 'vip'])) {
  ///   // 显示高级功能
  /// }
  /// ```
  bool hasAnyRole(List<String> requiredRoles) => requiredRoles.any(hasRole);
}

/// 认证状态枚举
///
/// 表示用户的认证状态，用于跟踪登录流程
///
/// 状态说明：
/// - [unknown]: 未知状态，通常是应用启动时的初始状态
/// - [authenticated]: 已认证状态，用户已成功登录
/// - [unauthenticated]: 未认证状态，用户未登录或登录已过期
///
/// 状态转换流程：
/// ```
/// unknown -> authenticated (登录成功)
/// unknown -> unauthenticated (初始化完成，无有效登录)
/// authenticated -> unauthenticated (登出或令牌过期)
/// unauthenticated -> authenticated (重新登录)
/// ```
enum AuthStatus {
  /// 未知状态
  ///
  /// 应用启动时的初始状态，表示还未完成认证状态检查
  /// 在此状态下通常显示加载界面
  unknown,

  /// 已认证状态
  ///
  /// 用户已成功登录，拥有有效的认证令牌
  /// 可以访问需要认证的功能和页面
  authenticated,

  /// 未认证状态
  ///
  /// 用户未登录或认证已过期
  /// 需要重新登录才能访问受保护的功能
  unauthenticated,
}

/// 认证服务类
///
/// 负责管理用户的认证状态、登录登出操作和权限验证
/// 使用单例模式确保全局状态一致性
///
/// 功能特性：
/// - 单例模式，全局唯一实例
/// - 状态变化通知（继承自 ChangeNotifier）
/// - 自动令牌管理和刷新
/// - 本地存储集成
/// - 角色权限检查
///
/// 使用示例：
/// ```dart
/// final authService = AuthService();
///
/// // 监听认证状态变化
/// authService.addListener(() {
///   if (authService.isAuthenticated) {
///     // 用户已登录
///   } else {
///     // 用户未登录
///   }
/// });
///
/// // 执行登录
/// final success = await authService.login('username', 'password');
/// if (success) {
///   // 登录成功
/// }
///
/// // 检查权限
/// if (authService.hasRole('admin')) {
///   // 用户是管理员
/// }
/// ```
class AuthService extends ChangeNotifier {
  /// 单例实例
  static final AuthService _instance = AuthService._internal();

  /// 获取认证服务的单例实例
  ///
  /// 返回值：
  /// - [AuthService]: 全局唯一的认证服务实例
  ///
  /// 使用示例：
  /// ```dart
  /// final authService = AuthService(); // 总是返回同一个实例
  /// final anotherRef = AuthService();  // 与上面是同一个对象
  /// print(identical(authService, anotherRef)); // true
  /// ```
  factory AuthService() => _instance;

  /// 私有构造函数，确保单例模式
  AuthService._internal();

  /// 当前认证状态
  /// 私有字段，通过 getter 访问
  AuthStatus _status = AuthStatus.unknown;

  /// 当前登录用户
  /// 私有字段，登录成功后设置，登出时清空
  User? _currentUser;

  /// 认证令牌
  /// 私有字段，用于API请求的身份验证
  String? _token;

  /// 获取当前认证状态
  ///
  /// 返回值：
  /// - [AuthStatus]: 当前的认证状态
  ///   - unknown: 未知状态（初始化中）
  ///   - authenticated: 已认证
  ///   - unauthenticated: 未认证
  ///
  /// 使用示例：
  /// ```dart
  /// switch (authService.status) {
  ///   case AuthStatus.unknown:
  ///     showLoadingScreen();
  ///     break;
  ///   case AuthStatus.authenticated:
  ///     showMainApp();
  ///     break;
  ///   case AuthStatus.unauthenticated:
  ///     showLoginScreen();
  ///     break;
  /// }
  /// ```
  AuthStatus get status => _status;

  /// 获取当前登录用户
  ///
  /// 返回值：
  /// - [User?]: 当前登录的用户对象，未登录时为 null
  ///
  /// 使用示例：
  /// ```dart
  /// final user = authService.currentUser;
  /// if (user != null) {
  ///   print('欢迎，${user.username}！');
  ///   print('邮箱：${user.email}');
  ///   print('角色：${user.roles.join(', ')}');
  /// } else {
  ///   print('用户未登录');
  /// }
  /// ```
  User? get currentUser => _currentUser;

  /// 获取当前认证令牌
  ///
  /// 返回值：
  /// - [String?]: 当前的认证令牌，未登录时为 null
  ///
  /// 使用场景：
  /// - API请求的身份验证
  /// - 令牌有效性检查
  /// - 手动构建认证头部
  ///
  /// 使用示例：
  /// ```dart
  /// final token = authService.token;
  /// if (token != null) {
  ///   // 添加到API请求头部
  ///   final headers = {
  ///     'Authorization': 'Bearer $token',
  ///     'Content-Type': 'application/json',
  ///   };
  /// }
  /// ```
  String? get token => _token;

  /// 检查用户是否已认证
  ///
  /// 返回值：
  /// - [bool]: true表示已认证，false表示未认证
  ///
  /// 使用场景：
  /// - 路由守卫权限检查
  /// - UI组件的条件渲染
  /// - 功能访问控制
  ///
  /// 使用示例：
  /// ```dart
  /// if (authService.isAuthenticated) {
  ///   // 显示用户相关功能
  ///   showUserProfile();
  ///   showProtectedContent();
  /// } else {
  ///   // 显示登录提示
  ///   showLoginPrompt();
  /// }
  /// ```
  bool get isAuthenticated => _status == AuthStatus.authenticated;

  /// 检查用户是否未认证
  ///
  /// 返回值：
  /// - [bool]: true表示未认证，false表示已认证或状态未知
  ///
  /// 使用场景：
  /// - 登录页面的显示控制
  /// - 游客模式功能
  /// - 认证提醒
  ///
  /// 使用示例：
  /// ```dart
  /// if (authService.isUnauthenticated) {
  ///   // 显示登录相关UI
  ///   showLoginButton();
  ///   hideProtectedFeatures();
  /// }
  /// ```
  bool get isUnauthenticated => _status == AuthStatus.unauthenticated;

  /// 初始化认证服务
  Future<void> initialize() async {
    try {
      // 这里可以从本地存储加载用户信息和令牌
      // 例如：SharedPreferences, SecureStorage 等
      await _loadStoredAuth();
      
      if (_token != null) {
        // 验证令牌是否有效
        final isValid = await _validateToken(_token!);
        if (isValid) {
          _status = AuthStatus.authenticated;
        } else {
          await logout();
        }
      } else {
        _status = AuthStatus.unauthenticated;
      }
    } catch (e) {
      debugPrint('认证初始化失败: $e');
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  /// 登录
  Future<bool> login(String username, String password) async {
    try {
      // 模拟API调用
      await Future.delayed(const Duration(seconds: 1));
      
      // 模拟登录验证
      if (username == 'admin' && password == 'password') {
        _currentUser = const User(
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          roles: ['admin', 'user'],
        );
        _token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        _status = AuthStatus.authenticated;
        
        // 保存到本地存储
        await _saveAuth();
        
        notifyListeners();
        return true;
      } else if (username == 'user' && password == 'password') {
        _currentUser = const User(
          id: '2',
          username: 'user',
          email: '<EMAIL>',
          roles: ['user'],
        );
        _token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        _status = AuthStatus.authenticated;
        
        await _saveAuth();
        
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('登录失败: $e');
      return false;
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      // 清除本地存储
      await _clearStoredAuth();
      
      _currentUser = null;
      _token = null;
      _status = AuthStatus.unauthenticated;
      
      notifyListeners();
    } catch (e) {
      debugPrint('登出失败: $e');
    }
  }

  /// 检查用户是否有指定角色
  bool hasRole(String role) {
    return _currentUser?.hasRole(role) ?? false;
  }

  /// 检查用户是否有任意指定角色
  bool hasAnyRole(List<String> roles) {
    return _currentUser?.hasAnyRole(roles) ?? false;
  }

  /// 刷新令牌
  Future<bool> refreshToken() async {
    try {
      if (_token == null) return false;
      
      // 模拟刷新令牌API调用
      await Future.delayed(const Duration(milliseconds: 500));
      
      _token = 'refreshed_token_${DateTime.now().millisecondsSinceEpoch}';
      await _saveAuth();
      
      return true;
    } catch (e) {
      debugPrint('刷新令牌失败: $e');
      return false;
    }
  }

  /// 从本地存储加载认证信息
  Future<void> _loadStoredAuth() async {
    try {
      // 这里应该从实际的本地存储加载
      // 例如：SharedPreferences, FlutterSecureStorage 等
      // 现在只是模拟
      await Future.delayed(const Duration(milliseconds: 100));

      // 模拟从存储中加载数据
      // final prefs = await SharedPreferences.getInstance();
      // _token = prefs.getString('auth_token');
      // final userJson = prefs.getString('current_user');
      // if (userJson != null) {
      //   _currentUser = User.fromJson(jsonDecode(userJson));
      // }
    } catch (e) {
      debugPrint('加载存储的认证信息失败: $e');
      // 如果加载失败，清除可能损坏的数据
      await _clearStoredAuth();
    }
  }

  /// 保存认证信息到本地存储
  Future<void> _saveAuth() async {
    try {
      // 这里应该保存到实际的本地存储
      await Future.delayed(const Duration(milliseconds: 100));

      // 模拟保存到存储
      // final prefs = await SharedPreferences.getInstance();
      // if (_token != null) {
      //   await prefs.setString('auth_token', _token!);
      // }
      // if (_currentUser != null) {
      //   await prefs.setString('current_user', jsonEncode(_currentUser!.toJson()));
      // }
    } catch (e) {
      debugPrint('保存认证信息失败: $e');
    }
  }

  /// 清除本地存储的认证信息
  Future<void> _clearStoredAuth() async {
    try {
      // 这里应该清除实际的本地存储
      await Future.delayed(const Duration(milliseconds: 100));

      // 模拟清除存储
      // final prefs = await SharedPreferences.getInstance();
      // await prefs.remove('auth_token');
      // await prefs.remove('current_user');
    } catch (e) {
      debugPrint('清除存储的认证信息失败: $e');
    }
  }

  /// 验证令牌是否有效
  Future<bool> _validateToken(String token) async {
    try {
      // 模拟令牌验证API调用
      await Future.delayed(const Duration(milliseconds: 300));

      // 简单的令牌格式验证
      return token.startsWith('mock_token_') || token.startsWith('refreshed_token_');
    } catch (e) {
      debugPrint('令牌验证失败: $e');
      return false;
    }
  }

  /// 获取用户显示名称
  String get userDisplayName {
    if (_currentUser == null) return '未登录';
    return _currentUser!.username;
  }

  /// 检查令牌是否即将过期
  bool get isTokenExpiringSoon {
    // 这里应该检查实际的令牌过期时间
    // 现在只是模拟逻辑
    return false;
  }

  /// 自动刷新令牌（如果需要）
  Future<void> autoRefreshTokenIfNeeded() async {
    if (isAuthenticated && isTokenExpiringSoon) {
      await refreshToken();
    }
  }

  /// 获取认证头部（用于API请求）
  Map<String, String>? get authHeaders {
    if (_token == null) return null;
    return {
      'Authorization': 'Bearer $_token',
      'Content-Type': 'application/json',
    };
  }

  /// 重置认证状态（用于测试）
  @visibleForTesting
  void resetForTesting() {
    _currentUser = null;
    _token = null;
    _status = AuthStatus.unknown;
    notifyListeners();
  }
}
