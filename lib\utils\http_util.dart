import 'dart:convert';
import 'package:http/http.dart' as http;

/// HttpUtil 网络请求工具类封装
class HttpUtil {
  // 单例模式
  static final HttpUtil _instance = HttpUtil._internal();

  factory HttpUtil() => _instance;

  HttpUtil._internal();

  // API 基础地址（可根据实际修改）
  final String _baseUrl = 'https://your.api.base.url';

  // Token（可选）
  String? _token;

  /// 设置全局 Token（请求自动携带）
  void setToken(String token) {
    _token = token;
  }

  /// 清除 Token
  void clearToken() {
    _token = null;
  }

  /// 构建请求头
  /// [contentType]：请求类型，如 application/json，application/x-www-form-urlencoded
  Map<String, String> _buildHeaders({String? contentType}) {
    final headers = {
      'Accept': 'application/json', // 响应类型
    };

    if (contentType != null) {
      headers['Content-Type'] = contentType;
    }

    // 添加 Authorization: Bearer token（如果有）
    if (_token != null && _token!.isNotEmpty) {
      headers['Authorization'] = 'Bearer $_token';
    }

    return headers;
  }

  /// GET 请求
  /// [path]：请求路径（不含 baseUrl）
  /// [params]：URL 查询参数（Map 格式）
  Future<dynamic> get(String path, {Map<String, String>? params}) async {
    try {
      final uri = Uri.parse('$_baseUrl$path').replace(queryParameters: params);
      final response = await http.get(uri, headers: _buildHeaders());
      return _handleResponse(response);
    } catch (e) {
      rethrow;
    }
  }

  /// POST 请求（JSON 格式）
  /// [path]：请求路径
  /// [body]：请求体参数，Map 格式，会被转为 JSON 字符串
  Future<dynamic> postJson(String path, {Map<String, dynamic>? body}) async {
    try {
      final uri = Uri.parse('$_baseUrl$path');
      final response = await http.post(
        uri,
        headers: _buildHeaders(contentType: 'application/json'),
        body: jsonEncode(body),
      );
      return _handleResponse(response);
    } catch (e) {
      rethrow;
    }
  }

  /// POST 请求（表单格式 application/x-www-form-urlencoded）
  /// [path]：请求路径
  /// [body]：表单参数，Map<String, String> 类型
  Future<dynamic> postForm(String path, {Map<String, String>? body}) async {
    try {
      final uri = Uri.parse('$_baseUrl$path');
      final response = await http.post(
        uri,
        headers: _buildHeaders(contentType: 'application/x-www-form-urlencoded'),
        body: body,
      );
      return _handleResponse(response);
    } catch (e) {
      rethrow;
    }
  }

  /// 统一响应处理
  dynamic _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final body = response.body.isNotEmpty ? json.decode(response.body) : null;

    if (statusCode >= 200 && statusCode < 300) {
      return body;
    } else {
      throw HttpException('请求失败：HTTP $statusCode', body: body);
    }
  }
}

/// 自定义异常类，返回更友好的错误信息
class HttpException implements Exception {
  final String message;
  final dynamic body;

  HttpException(this.message, {this.body});

  @override
  String toString() => 'HttpException: $message\n$body';
}
