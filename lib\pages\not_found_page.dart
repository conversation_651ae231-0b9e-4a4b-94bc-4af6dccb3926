import 'package:flutter/material.dart';
import '../router/app_routes.dart';
import '../router/app_router_delegate.dart';

class NotFoundPage extends StatelessWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('页面未找到'),
        centerTitle: true,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildErrorIcon(),
              const SizedBox(height: 32),
              _buildErrorMessage(context),
              const SizedBox(height: 48),
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorIcon() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.error_outline,
        size: 60,
        color: Colors.grey,
      ),
    );
  }

  Widget _buildErrorMessage(BuildContext context) {
    return Column(
      children: [
        Text(
          '404',
          style: Theme.of(context).textTheme.displayLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 16),
        Text(
          '页面未找到',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          '抱歉，您访问的页面不存在或已被移除。',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        ElevatedButton.icon(
          onPressed: () => _navigateToHome(context),
          icon: const Icon(Icons.home),
          label: const Text('返回首页'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
        ),
        const SizedBox(height: 12),
        TextButton.icon(
          onPressed: () => _goBack(context),
          icon: const Icon(Icons.arrow_back),
          label: const Text('返回上一页'),
        ),
      ],
    );
  }

  void _navigateToHome(BuildContext context) {
    // 使用 Router 2.0 清除所有页面并导航到首页
    final delegate = Router.of(context).routerDelegate as AppRouterDelegate;
    delegate.clearAndNavigateTo(AppRoutePath.home());
  }

  void _goBack(BuildContext context) {
    // 使用 Router 2.0 的返回功能
    final delegate = Router.of(context).routerDelegate as AppRouterDelegate;
    delegate.goBack();
  }
}
