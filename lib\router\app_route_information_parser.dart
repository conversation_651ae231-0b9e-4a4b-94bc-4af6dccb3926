import 'package:flutter/material.dart';
import 'app_routes.dart';

/// 应用路由信息解析器
class AppRouteInformationParser extends RouteInformationParser<AppRoutePath> {
  @override
  Future<AppRoutePath> parseRouteInformation(RouteInformation routeInformation) async {
    final uri = routeInformation.uri;

    if (uri.path.isEmpty) {
      return AppRoutePath.home();
    }

    try {
      return AppRoutePath.fromUri(uri);
    } catch (e) {
      debugPrint('解析路由信息失败: $e, uri: $uri');
      return AppRoutePath.notFound();
    }
  }

  @override
  RouteInformation? restoreRouteInformation(AppRoutePath configuration) {
    return RouteInformation(
      uri: configuration.toUri(),
      state: configuration.parameters.isNotEmpty ? configuration.parameters : null,
    );
  }
}
