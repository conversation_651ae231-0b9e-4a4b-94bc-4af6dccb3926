// 路由系统测试文件
// 
// 专门测试路由导航功能，确保 Router 2.0 正常工作

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:move_app_pro/main.dart';
import 'package:move_app_pro/router/app_router_delegate.dart';
import 'package:move_app_pro/router/app_routes.dart';

void main() {
  group('路由系统测试', () {
    
    /// 测试路由委托初始化
    test('路由委托初始化测试', () async {
      final delegate = AppRouterDelegate();
      
      // 验证初始状态
      expect(delegate.isInitialized, false);
      
      // 执行初始化
      await delegate.initialize();
      
      // 验证初始化完成
      expect(delegate.isInitialized, true);
      expect(delegate.currentConfiguration.location, AppRoutes.home);
    });
    
    /// 测试程序化导航（考虑路由守卫）
    test('程序化导航测试', () async {
      final delegate = AppRouterDelegate();
      await delegate.initialize();

      // 测试导航到详情页（未登录时会被重定向到登录页）
      await delegate.navigateToDetail();
      expect(delegate.currentConfiguration.location, AppRoutes.login,
             reason: '未登录用户访问详情页应该被重定向到登录页');

      // 测试直接导航到登录页
      await delegate.navigateToLogin();
      expect(delegate.currentConfiguration.location, AppRoutes.login);

      // 测试返回首页
      await delegate.navigateToHome();
      expect(delegate.currentConfiguration.location, AppRoutes.home);
    });
    
    /// 测试路由守卫集成
    test('路由守卫集成测试', () async {
      final delegate = AppRouterDelegate();
      await delegate.initialize();
      
      // 测试未登录时访问受保护页面
      // 应该被重定向到登录页
      await delegate.navigateToDetail();
      
      // 由于认证守卫的作用，应该被重定向到登录页
      expect(delegate.currentConfiguration.location, AppRoutes.login);
    });
    
    /// 测试应用启动
    testWidgets('应用启动测试', (WidgetTester tester) async {
      // 构建应用
      await tester.pumpWidget(const MyApp());

      // 等待初始化完成
      await tester.pumpAndSettle();

      // 验证首页加载
      expect(find.text('Move App Pro'), findsOneWidget);

      // 验证欢迎信息存在
      expect(find.textContaining('欢迎'), findsWidgets);

      // 验证应用基本结构
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });
  });
}
