// lib/utils/db_init.dart

import 'package:sqflite/sqflite.dart';
import 'db_util.dart';

class DBInit {
  /// 创建数据表
  static Future<void> onCreate(Database db, int version) async {

    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT,
        token TEXT
      );
    ''');

    await db.execute('''
      CREATE TABLE logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message TEXT,
        created_at TEXT
      );
    ''');
  }

  /// 数据库升级（可选）
  static Future<void> onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await db.execute('ALTER TABLE users ADD COLUMN email TEXT');
    }
    // 更多版本升级逻辑可添加
  }
}
