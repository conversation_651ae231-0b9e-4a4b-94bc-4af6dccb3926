// lib/utils/db_util.dart

import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'db_init.dart'; // 引入初始化逻辑

class DBUtil {
  static Database? _db;

  /// 初始化数据库（仅需调用一次）
  static Future<Database> init() async {
    if (_db != null) return _db!;

    String dbPath = await getDatabasesPath();
    String path = join(dbPath, 'app.db');

    _db = await openDatabase(
      path,
      version: 1,
      onCreate: DBInit.onCreate,     // 建表回调
      onUpgrade: DBInit.onUpgrade,   // 升级回调
    );

    return _db!;
  }

  /// 插入数据
  static Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await init();
    return await db.insert(table, data);
  }

  /// 查询数据
  static Future<List<Map<String, dynamic>>> query(String table, {String? where, List<dynamic>? whereArgs}) async {
    final db = await init();
    return await db.query(table, where: where, whereArgs: whereArgs);
  }

  /// 执行嵌套 SQL（事务）
  static Future<void> runTransaction(List<String> sqlList) async {
    final db = await init();
    await db.transaction((txn) async {
      for (String sql in sqlList) {
        await txn.execute(sql);
      }
    });
  }

  /// 删除数据库（调试用）
  static Future<void> deleteDb() async {
    String dbPath = await getDatabasesPath();
    String path = join(dbPath, 'app.db');
    await deleteDatabase(path);
    _db = null;
  }
}
