# 参数用法详细指南

## 📋 概述

本文档详细说明了 Move App Pro 项目中所有类、方法和函数的参数用法，包含完整的示例代码和最佳实践。

## 🔐 认证服务 (AuthService)

### 构造函数

```dart
// AuthService 使用单例模式，无需传递参数
final authService = AuthService();
```

### initialize() 方法

```dart
/// 初始化认证服务
/// 
/// 参数：无
/// 返回值：Future<void>
/// 
/// 功能：
/// - 从本地存储加载用户信息
/// - 验证存储的令牌
/// - 设置初始认证状态
await authService.initialize();
```

### login() 方法

```dart
/// 用户登录
/// 
/// 参数：
/// - username (String): 用户名，必填，不能为空
/// - password (String): 密码，必填，不能为空
/// 
/// 返回值：Future<bool>
/// - true: 登录成功
/// - false: 登录失败（用户名或密码错误）

// 基本用法
final success = await authService.login('admin', 'password');

// 错误处理
try {
  final success = await authService.login(username, password);
  if (success) {
    print('登录成功');
    // 导航到主页面
  } else {
    print('用户名或密码错误');
    // 显示错误提示
  }
} catch (e) {
  print('登录过程中发生错误: $e');
}

// 演示账户
await authService.login('admin', 'password');    // 管理员账户
await authService.login('user', 'password');     // 普通用户账户
```

### hasRole() 方法

```dart
/// 检查用户是否拥有指定角色
/// 
/// 参数：
/// - role (String): 要检查的角色名称，必填
/// 
/// 返回值：bool
/// - true: 用户拥有该角色
/// - false: 用户没有该角色或未登录

// 基本用法
if (authService.hasRole('admin')) {
  // 显示管理员功能
  showAdminPanel();
}

// 多种角色检查
final roles = ['admin', 'editor', 'moderator', 'user', 'vip'];
for (final role in roles) {
  if (authService.hasRole(role)) {
    print('用户拥有角色: $role');
  }
}

// 条件渲染
Widget buildUserMenu() {
  return Column(
    children: [
      if (authService.hasRole('user')) 
        ListTile(title: Text('用户设置')),
      if (authService.hasRole('admin')) 
        ListTile(title: Text('管理面板')),
      if (authService.hasRole('vip')) 
        ListTile(title: Text('VIP专区')),
    ],
  );
}
```

### hasAnyRole() 方法

```dart
/// 检查用户是否拥有指定角色列表中的任意一个角色
/// 
/// 参数：
/// - requiredRoles (List<String>): 要检查的角色列表，必填，不能为空
/// 
/// 返回值：bool
/// - true: 用户至少拥有其中一个角色
/// - false: 用户没有任何指定角色或未登录

// 基本用法 - 检查管理权限
if (authService.hasAnyRole(['admin', 'moderator'])) {
  // 用户有管理权限
  showManagementFeatures();
}

// 检查编辑权限
if (authService.hasAnyRole(['admin', 'editor', 'author'])) {
  // 用户可以编辑内容
  enableEditMode();
}

// 检查高级功能权限
final premiumRoles = ['admin', 'vip', 'premium'];
if (authService.hasAnyRole(premiumRoles)) {
  // 显示高级功能
  showPremiumFeatures();
} else {
  // 显示升级提示
  showUpgradePrompt();
}

// 空列表处理
if (authService.hasAnyRole([])) {
  // 永远返回 false
  print('空角色列表，返回 false');
}
```

## 🗺️ 路由系统

### AppRoutePath 构造函数

```dart
/// 创建路由路径对象
/// 
/// 参数：
/// - location (String): 路由路径，必填，必须是有效的路由路径
/// - queryParameters (Map<String, String>): 查询参数，可选，默认为空
/// - parameters (Map<String, dynamic>): 额外参数，可选，默认为空

// 基本路由
const route = AppRoutePath(location: '/home');

// 带查询参数的路由
const routeWithQuery = AppRoutePath(
  location: '/detail',
  queryParameters: {'id': '123', 'tab': 'info'},
);

// 带额外参数的路由
final routeWithParams = AppRoutePath(
  location: '/profile',
  queryParameters: {'userId': '456'},
  parameters: {
    'user': userObject,
    'callback': onProfileUpdated,
    'settings': settingsMap,
  },
);
```

### AppRoutePath.detail() 工厂方法

```dart
/// 创建详情页路由
/// 
/// 参数：
/// - queryParams (Map<String, String>?): 查询参数，可选

// 基础详情页
final detailRoute = AppRoutePath.detail();

// 带项目ID的详情页
final detailWithId = AppRoutePath.detail(
  queryParams: {'id': '123'}
);

// 带多个参数的详情页
final detailWithMultiParams = AppRoutePath.detail(
  queryParams: {
    'id': '123',           // 项目ID
    'tab': 'settings',     // 默认标签页
    'filter': 'active',    // 过滤条件
    'sort': 'date',        // 排序方式
    'page': '1',           // 页码
  }
);

// 实际使用示例
void navigateToProjectDetail(String projectId, {String? defaultTab}) {
  final params = <String, String>{'id': projectId};
  if (defaultTab != null) {
    params['tab'] = defaultTab;
  }
  
  final route = AppRoutePath.detail(queryParams: params);
  navigator.navigateTo(route);
}
```

### AppRoutePath.login() 工厂方法

```dart
/// 创建登录页路由
/// 
/// 参数：
/// - redirectTo (String?): 登录成功后重定向的路径，可选

// 普通登录页
final loginRoute = AppRoutePath.login();

// 登录后重定向到详情页
final loginWithRedirect = AppRoutePath.login(redirectTo: '/detail');

// 登录后重定向到带参数的页面
final loginWithComplexRedirect = AppRoutePath.login(
  redirectTo: '/detail?id=123&tab=info'
);

// 实际使用示例
void requireLogin({String? returnTo}) {
  final currentPath = returnTo ?? getCurrentPath();
  final loginRoute = AppRoutePath.login(redirectTo: currentPath);
  navigator.navigateTo(loginRoute);
}

// 在路由守卫中使用
if (!authService.isAuthenticated) {
  return GuardResponse.redirect(
    AppRoutePath.login(redirectTo: route.fullPath).fullPath,
    reason: '请先登录'
  );
}
```

### AppRoutePath.fromUri() 工厂方法

```dart
/// 从URI创建路由路径
/// 
/// 参数：
/// - uri (Uri): 要解析的URI对象，必填

// 解析简单路径
final uri1 = Uri.parse('/detail');
final route1 = AppRoutePath.fromUri(uri1);
// 结果：AppRoutePath(location: '/detail')

// 解析带查询参数的路径
final uri2 = Uri.parse('/detail?id=123&tab=info');
final route2 = AppRoutePath.fromUri(uri2);
// 结果：AppRoutePath(location: '/detail', queryParameters: {'id': '123', 'tab': 'info'})

// 解析复杂URI
final uri3 = Uri.parse('/detail?id=123&tab=info&filter=active&sort=date');
final route3 = AppRoutePath.fromUri(uri3);

// 解析无效路径
final uri4 = Uri.parse('/invalid-path');
final route4 = AppRoutePath.fromUri(uri4);
// 结果：AppRoutePath(location: '/404')

// 实际使用示例
void handleDeepLink(String url) {
  try {
    final uri = Uri.parse(url);
    final route = AppRoutePath.fromUri(uri);
    
    if (route.location == AppRoutes.notFound) {
      print('无效的深度链接: $url');
      showErrorDialog('链接无效');
    } else {
      navigator.navigateTo(route);
    }
  } catch (e) {
    print('解析深度链接失败: $e');
    showErrorDialog('链接格式错误');
  }
}
```

## 🛡️ 路由守卫系统

### GuardResponse 工厂方法

```dart
/// 创建允许访问的响应
/// 
/// 参数：无
/// 返回值：GuardResponse(canActivate: true)
final allowResponse = GuardResponse.allow();

/// 创建拒绝访问的响应
/// 
/// 参数：
/// - reason (String?): 拒绝原因，可选，默认为 '访问被拒绝'
final denyResponse = GuardResponse.deny(reason: '权限不足');

/// 创建重定向响应
/// 
/// 参数：
/// - redirectTo (String): 重定向目标路径，必填
/// - reason (String?): 重定向原因，可选，用于用户提示
final redirectResponse = GuardResponse.redirect('/login', reason: '请先登录');
```

### RoleGuard 构造函数

```dart
/// 创建角色守卫
/// 
/// 参数：
/// - requiredRoles (List<String>): 必需的角色列表，必填，不能为空

// 单角色验证
final adminGuard = RoleGuard(requiredRoles: ['admin']);

// 多角色验证（满足其中一个即可）
final editorGuard = RoleGuard(requiredRoles: ['admin', 'editor']);

// 高级权限验证
final premiumGuard = RoleGuard(requiredRoles: ['admin', 'vip', 'premium']);

// 实际使用示例
void setupRouteGuards() {
  final manager = RouteGuardManager();
  
  // 为管理页面添加管理员权限
  manager.addRouteGuard('/admin', RoleGuard(requiredRoles: ['admin']));
  
  // 为编辑页面添加编辑权限
  manager.addRouteGuard('/edit', RoleGuard(requiredRoles: ['admin', 'editor']));
  
  // 为VIP页面添加VIP权限
  manager.addRouteGuard('/vip', RoleGuard(requiredRoles: ['vip', 'premium']));
}
```

### CustomGuard 构造函数

```dart
/// 创建自定义守卫
/// 
/// 参数：
/// - guardFunction (Future<GuardResponse> Function(AppRoutePath)): 自定义验证函数，必填
/// - priority (int): 守卫优先级，可选，默认为100

// 时间限制守卫
final timeGuard = CustomGuard(
  priority: 30,
  guardFunction: (route) async {
    final hour = DateTime.now().hour;
    if (hour >= 9 && hour <= 18) {
      return GuardResponse.allow();
    } else {
      return GuardResponse.deny(reason: '仅在工作时间（9:00-18:00）可访问');
    }
  },
);

// 网络状态守卫
final networkGuard = CustomGuard(
  priority: 5,
  guardFunction: (route) async {
    final isOnline = await checkNetworkConnection();
    if (isOnline) {
      return GuardResponse.allow();
    } else {
      return GuardResponse.deny(reason: '需要网络连接');
    }
  },
);

// 设备权限守卫
final permissionGuard = CustomGuard(
  guardFunction: (route) async {
    final hasPermission = await checkDevicePermissions();
    if (hasPermission) {
      return GuardResponse.allow();
    } else {
      return GuardResponse.redirect('/permissions', reason: '需要设备权限');
    }
  },
);

// 复杂业务逻辑守卫
final businessGuard = CustomGuard(
  priority: 40,
  guardFunction: (route) async {
    // 检查用户订阅状态
    final user = AuthService().currentUser;
    if (user == null) {
      return GuardResponse.redirect('/login');
    }
    
    final subscription = await getSubscriptionStatus(user.id);
    if (subscription.isActive) {
      return GuardResponse.allow();
    } else if (subscription.isExpired) {
      return GuardResponse.redirect('/renew', reason: '订阅已过期，请续费');
    } else {
      return GuardResponse.redirect('/subscribe', reason: '需要订阅服务');
    }
  },
);
```

### RouteGuardManager 方法

```dart
/// 添加全局守卫
/// 
/// 参数：
/// - guard (RouteGuard): 要添加的守卫实例，必填
manager.addGlobalGuard(AuthGuard());

/// 为特定路由添加守卫
/// 
/// 参数：
/// - route (String): 路由路径，必填
/// - guard (RouteGuard): 要添加的守卫实例，必填
manager.addRouteGuard('/admin', RoleGuard(requiredRoles: ['admin']));

/// 移除守卫
/// 
/// 参数：
/// - guard (RouteGuard): 要移除的守卫实例，必填
manager.removeGuard(someGuard);

/// 检查路由是否可以激活
/// 
/// 参数：
/// - route (AppRoutePath): 要检查的路由对象，必填
/// 
/// 返回值：Future<GuardResponse>
final response = await manager.canActivate(route);
```

## 🎨 UI 组件参数

### HomePage 构造函数

```dart
/// 首页组件
/// 
/// 参数：
/// - key (Key?): Widget键，可选
const HomePage({super.key});
```

### LoginPage 构造函数

```dart
/// 登录页组件
/// 
/// 参数：
/// - key (Key?): Widget键，可选
/// - redirectTo (String?): 登录成功后重定向路径，可选
const LoginPage({super.key, this.redirectTo});

// 使用示例
LoginPage() // 普通登录页
LoginPage(redirectTo: '/detail') // 登录后重定向到详情页
```

### DetailPage 构造函数

```dart
/// 详情页组件
/// 
/// 参数：
/// - key (Key?): Widget键，可选
const DetailPage({super.key});
```

## 📝 最佳实践

### 1. 参数验证

```dart
// 在方法开始时验证参数
Future<bool> login(String username, String password) async {
  // 参数验证
  if (username.isEmpty) {
    throw ArgumentError('用户名不能为空');
  }
  if (password.isEmpty) {
    throw ArgumentError('密码不能为空');
  }
  
  // 执行登录逻辑
  // ...
}
```

### 2. 可选参数的默认值

```dart
// 为可选参数提供合理的默认值
AppRoutePath.detail({Map<String, String>? queryParams}) {
  return AppRoutePath(
    location: AppRoutes.detail,
    queryParameters: queryParams ?? {}, // 提供默认空Map
  );
}
```

### 3. 参数文档

```dart
/// 方法说明
/// 
/// 参数说明：
/// - [param1]: 参数1的说明，包括类型、是否必填、默认值
/// - [param2]: 参数2的说明
/// 
/// 返回值：
/// - 返回值的说明
/// 
/// 异常：
/// - 可能抛出的异常说明
/// 
/// 使用示例：
/// ```dart
/// final result = await method(param1, param2);
/// ```
```

这份文档涵盖了项目中所有主要组件的参数用法，每个参数都有详细的说明和实际使用示例。
