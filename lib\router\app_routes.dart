/// 应用路由路径常量类
///
/// 集中管理应用中所有的路由路径，提供统一的路由配置
///
/// 功能特性：
/// - 统一管理所有路由路径常量
/// - 区分公开路由和受保护路由
/// - 支持路由分类和权限控制
/// - 便于路由的维护和管理
///
/// 使用示例：
/// ```dart
/// // 导航到首页
/// Navigator.pushNamed(context, AppRoutes.home);
///
/// // 导航到详情页
/// Navigator.pushNamed(context, AppRoutes.detail);
///
/// // 检查路由是否需要认证
/// if (AppRoutes.protectedRoutes.contains(currentRoute)) {
///   // 需要认证的路由
/// }
/// ```
class AppRoutes {
  /// 首页路径
  ///
  /// 应用的主页面，通常是用户进入应用后看到的第一个页面
  /// 路径：'/'
  /// 权限：公开访问，无需登录
  static const String home = '/';

  /// 详情页路径
  ///
  /// 显示详细信息的页面，包含项目列表、网格视图和设置
  /// 路径：'/detail'
  /// 权限：需要用户登录后访问
  static const String detail = '/detail';

  /// 登录页路径
  ///
  /// 用户认证页面，提供用户名密码登录功能
  /// 路径：'/login'
  /// 权限：公开访问，支持重定向参数
  static const String login = '/login';

  /// 404错误页路径
  ///
  /// 页面未找到时显示的错误页面
  /// 路径：'/404'
  /// 权限：公开访问
  static const String notFound = '/404';

  /// 所有路由路径列表
  ///
  /// 包含应用中定义的所有有效路由路径
  /// 用于路由验证和导航控制
  ///
  /// 使用场景：
  /// - 验证路由路径是否有效
  /// - 生成路由菜单
  /// - 路由权限检查
  static const List<String> allRoutes = [
    home,
    detail,
    login,
    notFound,
  ];

  /// 需要认证的路由列表
  ///
  /// 这些路由需要用户登录后才能访问
  /// 未登录用户访问时会被重定向到登录页
  ///
  /// 使用场景：
  /// - 路由守卫权限检查
  /// - 自动重定向到登录页
  /// - 用户权限验证
  ///
  /// 添加新的受保护路由：
  /// ```dart
  /// static const List<String> protectedRoutes = [
  ///   detail,
  ///   '/profile',    // 用户资料页
  ///   '/settings',   // 设置页
  ///   '/admin',      // 管理页面
  /// ];
  /// ```
  static const List<String> protectedRoutes = [
    detail,
  ];

  /// 公开路由列表（不需要认证）
  ///
  /// 这些路由可以在未登录状态下访问
  /// 通常包括首页、登录页、注册页、帮助页等
  ///
  /// 使用场景：
  /// - 路由守卫跳过认证检查
  /// - 游客模式访问控制
  /// - SEO友好的公开页面
  ///
  /// 添加新的公开路由：
  /// ```dart
  /// static const List<String> publicRoutes = [
  ///   home,
  ///   login,
  ///   notFound,
  ///   '/about',      // 关于页面
  ///   '/help',       // 帮助页面
  ///   '/register',   // 注册页面
  /// ];
  /// ```
  static const List<String> publicRoutes = [
    home,
    login,
    notFound,
  ];
}

/// 路由路径配置类
///
/// 封装路由的完整信息，包括路径、查询参数和额外参数
///
/// 功能特性：
/// - 类型安全的路由对象
/// - 支持查询参数和自定义参数
/// - 提供便捷的工厂方法
/// - 支持路由权限检查
///
/// 使用示例：
/// ```dart
/// // 创建简单路由
/// final homeRoute = AppRoutePath.home();
///
/// // 创建带查询参数的路由
/// final detailRoute = AppRoutePath.detail(
///   queryParams: {'id': '123', 'tab': 'info'}
/// );
///
/// // 创建登录路由并指定重定向
/// final loginRoute = AppRoutePath.login(redirectTo: '/detail');
///
/// // 从URI创建路由
/// final route = AppRoutePath.fromUri(Uri.parse('/detail?id=123'));
/// ```
class AppRoutePath {
  /// 路由路径
  ///
  /// 表示路由的基础路径，如 '/', '/detail', '/login' 等
  /// 必须是 AppRoutes 中定义的有效路径
  final String location;

  /// 查询参数
  ///
  /// URL中的查询参数，如 ?id=123&tab=info
  /// 格式：Map<参数名, 参数值>
  ///
  /// 示例：
  /// ```dart
  /// // URL: /detail?id=123&tab=info
  /// queryParameters = {'id': '123', 'tab': 'info'}
  /// ```
  final Map<String, String> queryParameters;

  /// 额外参数
  ///
  /// 用于传递复杂对象或内部状态，不会出现在URL中
  /// 可以存储任意类型的数据
  ///
  /// 示例：
  /// ```dart
  /// parameters = {
  ///   'user': userObject,
  ///   'settings': settingsMap,
  ///   'callback': callbackFunction,
  /// }
  /// ```
  final Map<String, dynamic> parameters;

  /// 创建路由路径对象
  ///
  /// 参数说明：
  /// - [location]: 路由路径，必填，必须是有效的路由路径
  /// - [queryParameters]: 查询参数，可选，默认为空
  /// - [parameters]: 额外参数，可选，默认为空
  ///
  /// 使用示例：
  /// ```dart
  /// final route = AppRoutePath(
  ///   location: '/detail',
  ///   queryParameters: {'id': '123'},
  ///   parameters: {'data': someObject},
  /// );
  /// ```
  const AppRoutePath({
    required this.location,
    this.queryParameters = const {},
    this.parameters = const {},
  });

  /// 创建首页路由
  ///
  /// 返回指向应用首页的路由对象
  /// 路径：'/'
  ///
  /// 使用示例：
  /// ```dart
  /// final homeRoute = AppRoutePath.home();
  /// await navigator.navigateTo(homeRoute);
  /// ```
  factory AppRoutePath.home() => const AppRoutePath(location: AppRoutes.home);

  /// 创建详情页路由
  ///
  /// 参数说明：
  /// - [queryParams]: 查询参数，可选
  ///   - 常用参数：id（项目ID）、tab（标签页）、filter（过滤条件）
  ///
  /// 使用示例：
  /// ```dart
  /// // 基础详情页
  /// final detailRoute = AppRoutePath.detail();
  ///
  /// // 带参数的详情页
  /// final detailRoute = AppRoutePath.detail(
  ///   queryParams: {
  ///     'id': '123',           // 项目ID
  ///     'tab': 'settings',     // 默认标签页
  ///     'filter': 'active',    // 过滤条件
  ///   }
  /// );
  /// ```
  factory AppRoutePath.detail({Map<String, String>? queryParams}) {
    return AppRoutePath(
      location: AppRoutes.detail,
      queryParameters: queryParams ?? {},
    );
  }

  /// 创建登录页路由
  ///
  /// 参数说明：
  /// - [redirectTo]: 登录成功后重定向的路径，可选
  ///   - 如果提供，登录成功后会自动跳转到指定页面
  ///   - 如果不提供，登录成功后跳转到首页
  ///
  /// 使用示例：
  /// ```dart
  /// // 普通登录页
  /// final loginRoute = AppRoutePath.login();
  ///
  /// // 登录后重定向到详情页
  /// final loginRoute = AppRoutePath.login(redirectTo: '/detail');
  ///
  /// // 登录后重定向到带参数的页面
  /// final loginRoute = AppRoutePath.login(
  ///   redirectTo: '/detail?id=123&tab=info'
  /// );
  /// ```
  factory AppRoutePath.login({String? redirectTo}) {
    return AppRoutePath(
      location: AppRoutes.login,
      queryParameters: redirectTo != null ? {'redirect': redirectTo} : {},
    );
  }

  /// 创建404页面路由
  ///
  /// 返回指向404错误页面的路由对象
  /// 用于处理无效路径或页面未找到的情况
  ///
  /// 使用示例：
  /// ```dart
  /// // 当路由解析失败时
  /// if (!isValidRoute(path)) {
  ///   return AppRoutePath.notFound();
  /// }
  ///
  /// // 在路由守卫中
  /// if (!hasPermission) {
  ///   return GuardResponse.redirect(AppRoutePath.notFound().fullPath);
  /// }
  /// ```
  factory AppRoutePath.notFound() => const AppRoutePath(location: AppRoutes.notFound);

  /// 从URI创建路由路径
  ///
  /// 解析URI并创建对应的路由路径对象
  /// 如果URI路径无效，返回404路由
  ///
  /// 参数说明：
  /// - [uri]: 要解析的URI对象
  ///
  /// 解析规则：
  /// 1. 提取URI的路径部分
  /// 2. 提取查询参数
  /// 3. 验证路径是否在有效路由列表中
  /// 4. 如果有效，创建对应路由对象；否则返回404路由
  ///
  /// 使用示例：
  /// ```dart
  /// // 解析简单路径
  /// final uri1 = Uri.parse('/detail');
  /// final route1 = AppRoutePath.fromUri(uri1);
  /// // 结果：AppRoutePath(location: '/detail')
  ///
  /// // 解析带查询参数的路径
  /// final uri2 = Uri.parse('/detail?id=123&tab=info');
  /// final route2 = AppRoutePath.fromUri(uri2);
  /// // 结果：AppRoutePath(location: '/detail', queryParameters: {'id': '123', 'tab': 'info'})
  ///
  /// // 解析无效路径
  /// final uri3 = Uri.parse('/invalid-path');
  /// final route3 = AppRoutePath.fromUri(uri3);
  /// // 结果：AppRoutePath(location: '/404')
  /// ```
  factory AppRoutePath.fromUri(Uri uri) {
    final path = uri.path;
    final queryParams = uri.queryParameters;

    // 检查路径是否在有效路由列表中
    if (AppRoutes.allRoutes.contains(path)) {
      return AppRoutePath(
        location: path,
        queryParameters: queryParams,
      );
    }

    // 无效路径，返回404路由
    return AppRoutePath.notFound();
  }

  /// 转换为URI对象
  ///
  /// 将路由路径对象转换为标准的URI对象
  /// 包含路径和查询参数信息
  ///
  /// 返回值：
  /// - [Uri]: 包含路径和查询参数的URI对象
  ///
  /// 使用示例：
  /// ```dart
  /// final route = AppRoutePath.detail(
  ///   queryParams: {'id': '123', 'tab': 'info'}
  /// );
  /// final uri = route.toUri();
  /// print(uri.toString()); // 输出：/detail?id=123&tab=info
  ///
  /// // 用于浏览器导航
  /// html.window.history.pushState(null, '', uri.toString());
  /// ```
  Uri toUri() {
    return Uri(
      path: location,
      queryParameters: queryParameters.isNotEmpty ? queryParameters : null,
    );
  }

  /// 获取完整的路径字符串（包含查询参数）
  ///
  /// 返回包含路径和查询参数的完整URL字符串
  /// 便于在导航、日志记录和调试中使用
  ///
  /// 返回值：
  /// - [String]: 完整的路径字符串
  ///
  /// 使用示例：
  /// ```dart
  /// final route = AppRoutePath.detail(
  ///   queryParams: {'id': '123', 'tab': 'info'}
  /// );
  /// print(route.fullPath); // 输出：/detail?id=123&tab=info
  ///
  /// // 用于日志记录
  /// logger.info('导航到: ${route.fullPath}');
  ///
  /// // 用于重定向
  /// return GuardResponse.redirect(route.fullPath);
  /// ```
  String get fullPath {
    final uri = toUri();
    return uri.toString();
  }

  /// 检查是否是受保护的路由
  ///
  /// 判断当前路由是否需要用户认证才能访问
  ///
  /// 返回值：
  /// - [bool]: true表示需要认证，false表示不需要
  ///
  /// 使用场景：
  /// - 路由守卫中的权限检查
  /// - 导航前的预检查
  /// - UI状态的条件渲染
  ///
  /// 使用示例：
  /// ```dart
  /// final route = AppRoutePath.detail();
  /// if (route.isProtected && !authService.isAuthenticated) {
  ///   // 需要登录
  ///   navigateToLogin();
  /// }
  /// ```
  bool get isProtected => AppRoutes.protectedRoutes.contains(location);

  /// 检查是否是公开路由
  ///
  /// 判断当前路由是否可以在未登录状态下访问
  ///
  /// 返回值：
  /// - [bool]: true表示公开访问，false表示需要权限
  ///
  /// 使用场景：
  /// - 路由守卫中跳过认证检查
  /// - 游客模式的功能控制
  /// - SEO和搜索引擎优化
  ///
  /// 使用示例：
  /// ```dart
  /// final route = AppRoutePath.home();
  /// if (route.isPublic) {
  ///   // 公开路由，无需认证检查
  ///   allowAccess();
  /// }
  /// ```
  bool get isPublic => AppRoutes.publicRoutes.contains(location);

  /// 重写相等性比较操作符
  ///
  /// 比较两个 AppRoutePath 对象是否相等
  /// 比较规则：路径、查询参数和额外参数都相同才认为相等
  ///
  /// 参数说明：
  /// - [other]: 要比较的对象
  ///
  /// 返回值：
  /// - [bool]: true表示相等，false表示不相等
  ///
  /// 使用场景：
  /// - 路由状态比较
  /// - 缓存键值判断
  /// - 导航历史管理
  ///
  /// 使用示例：
  /// ```dart
  /// final route1 = AppRoutePath.detail(queryParams: {'id': '123'});
  /// final route2 = AppRoutePath.detail(queryParams: {'id': '123'});
  /// final route3 = AppRoutePath.detail(queryParams: {'id': '456'});
  ///
  /// print(route1 == route2); // true，路径和参数都相同
  /// print(route1 == route3); // false，参数不同
  /// ```
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppRoutePath &&
        other.location == location &&
        _mapEquals(other.queryParameters, queryParameters) &&
        _mapEquals(other.parameters, parameters);
  }

  /// 重写哈希码计算
  ///
  /// 基于路径、查询参数和额外参数计算哈希码
  /// 确保相等的对象具有相同的哈希码
  ///
  /// 返回值：
  /// - [int]: 对象的哈希码
  ///
  /// 使用场景：
  /// - Set和Map中的键值比较
  /// - 对象缓存和去重
  /// - 性能优化的快速比较
  @override
  int get hashCode {
    return Object.hash(
      location,
      Object.hashAll(queryParameters.entries.map((e) => Object.hash(e.key, e.value))),
      Object.hashAll(parameters.entries.map((e) => Object.hash(e.key, e.value))),
    );
  }

  /// 重写字符串表示
  ///
  /// 返回对象的字符串表示，便于调试和日志记录
  ///
  /// 返回值：
  /// - [String]: 包含所有字段信息的字符串
  ///
  /// 使用示例：
  /// ```dart
  /// final route = AppRoutePath.detail(
  ///   queryParams: {'id': '123'},
  ///   parameters: {'data': 'test'}
  /// );
  /// print(route.toString());
  /// // 输出：AppRoutePath(location: /detail, queryParameters: {id: 123}, parameters: {data: test})
  ///
  /// // 用于日志记录
  /// logger.debug('当前路由: $route');
  /// ```
  @override
  String toString() {
    return 'AppRoutePath(location: $location, queryParameters: $queryParameters, parameters: $parameters)';
  }

  /// 辅助方法：比较两个Map是否相等
  ///
  /// 深度比较两个Map的内容是否完全相同
  ///
  /// 参数说明：
  /// - [a]: 第一个Map，可为null
  /// - [b]: 第二个Map，可为null
  ///
  /// 返回值：
  /// - [bool]: true表示相等，false表示不相等
  ///
  /// 比较规则：
  /// - 两个都为null时相等
  /// - 一个为null另一个不为null时不相等
  /// - 长度不同时不相等
  /// - 逐个比较键值对，都相同时相等
  bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    if (identical(a, b)) return true;
    for (final K key in a.keys) {
      if (!b.containsKey(key) || b[key] != a[key]) return false;
    }
    return true;
  }
}
