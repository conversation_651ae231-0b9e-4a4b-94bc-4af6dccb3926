import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../pages/home_page.dart';
import '../pages/detail_page.dart';
import '../pages/login_page.dart';
import '../pages/not_found_page.dart';
import 'app_routes.dart';
import 'route_guards.dart';

/// 应用路由委托
///
/// 负责管理应用的路由导航和页面栈
/// 实现了 Flutter Router 2.0 的核心功能
///
/// 功能特性：
/// - 声明式路由管理
/// - 路由守卫集成
/// - 认证状态监听
/// - 页面栈管理
/// - 深度链接支持
///
/// 使用示例：
/// ```dart
/// final delegate = AppRouterDelegate();
///
/// // 在 MaterialApp.router 中使用
/// MaterialApp.router(
///   routerDelegate: delegate,
///   routeInformationParser: AppRouteInformationParser(),
/// );
///
/// // 程序化导航
/// await delegate.navigateToDetail();
/// await delegate.navigateToLogin(redirectTo: '/detail');
/// ```
class AppRouterDelegate extends RouterDelegate<AppRoutePath>
    with ChangeNotifier, PopNavigatorRouterDelegateMixin<AppRoutePath> {

  /// Navigator 的全局键
  /// 用于访问 Navigator 状态和执行导航操作
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  /// 认证服务实例
  /// 用于监听用户登录状态变化
  final AuthService _authService = AuthService();

  /// 路由守卫管理器
  /// 负责执行路由访问权限检查
  final RouteGuardManager _guardManager = RouteGuardManager();

  /// 当前路由配置
  /// 表示当前激活的路由状态
  AppRoutePath _currentConfiguration = AppRoutePath.home();

  /// 页面栈
  /// 存储当前导航栈中的所有页面
  final List<Page> _pages = [];

  /// 初始化状态标志
  /// 标记路由委托是否已完成初始化
  bool _isInitialized = false;

  /// 创建路由委托实例
  ///
  /// 构造函数中会：
  /// 1. 监听认证状态变化
  /// 2. 初始化默认路由守卫
  ///
  /// 使用示例：
  /// ```dart
  /// final delegate = AppRouterDelegate();
  /// // 委托会自动设置认证监听和守卫
  /// ```
  AppRouterDelegate() {
    // 监听认证状态变化，当用户登录/登出时自动处理路由
    _authService.addListener(_onAuthStateChanged);

    // 初始化默认的路由守卫（如认证守卫）
    _guardManager.initializeDefaultGuards();
  }

  /// Navigator 的全局键
  ///
  /// RouterDelegate 接口要求的属性
  /// 用于 Flutter 框架访问 Navigator 状态
  ///
  /// 返回值：
  /// - [GlobalKey<NavigatorState>]: Navigator 的全局键
  @override
  GlobalKey<NavigatorState> get navigatorKey => _navigatorKey;

  /// 当前路由配置
  ///
  /// RouterDelegate 接口要求的属性
  /// 返回当前激活的路由配置信息
  ///
  /// 返回值：
  /// - [AppRoutePath]: 当前路由配置对象
  ///
  /// 使用场景：
  /// - 浏览器地址栏同步
  /// - 深度链接处理
  /// - 路由状态持久化
  @override
  AppRoutePath get currentConfiguration => _currentConfiguration;

  /// 检查路由委托是否已初始化
  ///
  /// 返回值：
  /// - [bool]: true表示已初始化，false表示未初始化
  ///
  /// 使用场景：
  /// - 在导航前检查初始化状态
  /// - 条件渲染加载界面
  /// - 防止未初始化时的操作
  ///
  /// 使用示例：
  /// ```dart
  /// if (delegate.isInitialized) {
  ///   // 可以安全地进行导航操作
  ///   await delegate.navigateToDetail();
  /// } else {
  ///   // 显示加载界面或等待初始化
  ///   showLoadingScreen();
  /// }
  /// ```
  bool get isInitialized => _isInitialized;

  /// 初始化路由器
  ///
  /// 执行路由委托的初始化操作，包括：
  /// 1. 初始化认证服务
  /// 2. 设置初始页面
  /// 3. 设置初始化完成标志
  /// 4. 通知监听器更新UI
  ///
  /// 注意：
  /// - 此方法是幂等的，多次调用不会重复初始化
  /// - 初始化完成前，build 方法会显示加载界面
  ///
  /// 使用示例：
  /// ```dart
  /// final delegate = AppRouterDelegate();
  /// await delegate.initialize(); // 手动初始化
  ///
  /// // 或者在 main.dart 中自动初始化
  /// class _MyAppState extends State<MyApp> {
  ///   @override
  ///   void initState() {
  ///     super.initState();
  ///     _initializeRouter();
  ///   }
  ///
  ///   Future<void> _initializeRouter() async {
  ///     await _routerDelegate.initialize();
  ///   }
  /// }
  /// ```
  Future<void> initialize() async {
    // 防止重复初始化
    if (_isInitialized) return;

    // 初始化认证服务（加载存储的用户信息、验证令牌等）
    await _authService.initialize();

    // 设置初始页面（首页）
    if (_pages.isEmpty) {
      final homePage = _createPage(AppRoutePath.home());
      if (homePage != null) {
        _pages.add(homePage);
        _currentConfiguration = AppRoutePath.home();
      }
    }

    // 标记初始化完成
    _isInitialized = true;

    // 通知监听器（通常是 MaterialApp.router）更新UI
    notifyListeners();
  }

  /// 构建路由器的UI
  ///
  /// RouterDelegate 接口要求的方法
  /// 根据当前状态构建相应的UI界面
  ///
  /// 构建逻辑：
  /// 1. 如果未初始化，显示加载界面
  /// 2. 如果已初始化，显示包含页面栈的 Navigator
  ///
  /// 参数说明：
  /// - [context]: 构建上下文
  ///
  /// 返回值：
  /// - [Widget]: 路由器的UI组件
  ///
  /// 注意：
  /// - 加载界面使用独立的 MaterialApp，确保在任何情况下都能显示
  /// - Navigator 使用不可修改的页面列表，保证状态一致性
  @override
  Widget build(BuildContext context) {
    // 如果路由器未初始化，显示加载界面
    if (!_isInitialized) {
      return const MaterialApp(
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    // 构建包含页面栈的 Navigator
    return Navigator(
      key: navigatorKey,
      pages: List.unmodifiable(_pages), // 使用不可修改列表确保状态安全
      onDidRemovePage: _onDidRemovePage, // 处理页面移除事件
    );
  }

  /// 设置新的路由路径
  ///
  /// RouterDelegate 接口要求的方法
  /// 当浏览器地址栏变化或深度链接触发时调用
  ///
  /// 参数说明：
  /// - [configuration]: 新的路由配置
  ///
  /// 使用场景：
  /// - 浏览器前进/后退按钮
  /// - 地址栏直接输入
  /// - 深度链接跳转
  ///
  /// 示例：
  /// ```dart
  /// // 用户在地址栏输入 /detail?id=123
  /// // 系统会自动调用此方法，传入解析后的 AppRoutePath
  /// ```
  @override
  Future<void> setNewRoutePath(AppRoutePath configuration) async {
    await _navigateToRoute(configuration);
  }

  /// 导航到指定路由
  ///
  /// 通用的导航方法，支持导航到任意路由
  /// 会自动执行路由守卫检查
  ///
  /// 参数说明：
  /// - [route]: 目标路由配置
  ///
  /// 使用示例：
  /// ```dart
  /// // 导航到带参数的详情页
  /// await delegate.navigateTo(
  ///   AppRoutePath.detail(queryParams: {'id': '123'})
  /// );
  ///
  /// // 导航到登录页并指定重定向
  /// await delegate.navigateTo(
  ///   AppRoutePath.login(redirectTo: '/detail')
  /// );
  /// ```
  Future<void> navigateTo(AppRoutePath route) async {
    await _navigateToRoute(route);
  }

  /// 导航到首页
  ///
  /// 便捷方法，导航到应用首页
  ///
  /// 使用示例：
  /// ```dart
  /// // 用户点击首页按钮
  /// await delegate.navigateToHome();
  ///
  /// // 登出后返回首页
  /// await authService.logout();
  /// await delegate.navigateToHome();
  /// ```
  Future<void> navigateToHome() async {
    await navigateTo(AppRoutePath.home());
  }

  /// 导航到详情页
  ///
  /// 便捷方法，导航到详情页面
  /// 注意：此方法导航到基础详情页，不带查询参数
  ///
  /// 使用示例：
  /// ```dart
  /// // 用户点击详情按钮
  /// await delegate.navigateToDetail();
  ///
  /// // 如需带参数，使用通用方法
  /// await delegate.navigateTo(
  ///   AppRoutePath.detail(queryParams: {'id': '123'})
  /// );
  /// ```
  Future<void> navigateToDetail() async {
    await navigateTo(AppRoutePath.detail());
  }

  /// 导航到登录页
  ///
  /// 便捷方法，导航到登录页面
  /// 支持指定登录成功后的重定向路径
  ///
  /// 参数说明：
  /// - [redirectTo]: 登录成功后重定向的路径，可选
  ///
  /// 使用示例：
  /// ```dart
  /// // 普通登录
  /// await delegate.navigateToLogin();
  ///
  /// // 登录后重定向到详情页
  /// await delegate.navigateToLogin(redirectTo: '/detail');
  ///
  /// // 登录后重定向到带参数的页面
  /// await delegate.navigateToLogin(
  ///   redirectTo: '/detail?id=123&tab=info'
  /// );
  /// ```
  Future<void> navigateToLogin({String? redirectTo}) async {
    await navigateTo(AppRoutePath.login(redirectTo: redirectTo));
  }

  /// 返回上一页
  ///
  /// 从页面栈中移除最后一个页面，实现后退功能
  /// 如果只有一个页面，则不执行任何操作
  ///
  /// 使用场景：
  /// - 用户点击返回按钮
  /// - 程序化的页面后退
  /// - 取消操作后返回
  ///
  /// 注意：
  /// - 至少保留一个页面在栈中
  /// - 会自动更新当前路由配置
  /// - 会通知监听器更新UI
  ///
  /// 使用示例：
  /// ```dart
  /// // 用户点击返回按钮
  /// delegate.goBack();
  ///
  /// // 在页面中使用
  /// AppBar(
  ///   leading: IconButton(
  ///     icon: Icon(Icons.arrow_back),
  ///     onPressed: () => delegate.goBack(),
  ///   ),
  /// )
  /// ```
  void goBack() {
    // 确保至少保留一个页面
    if (_pages.length > 1) {
      _pages.removeLast();
      _updateCurrentConfiguration();
      notifyListeners();
    }
  }

  /// 清除所有页面并导航到指定路由
  ///
  /// 清空当前页面栈，然后导航到新路由
  /// 相当于重置导航历史
  ///
  /// 参数说明：
  /// - [route]: 要导航到的新路由
  ///
  /// 使用场景：
  /// - 用户登出后重置到首页
  /// - 完成引导流程后进入主界面
  /// - 错误恢复时重置导航状态
  ///
  /// 使用示例：
  /// ```dart
  /// // 登出后清除历史并返回首页
  /// await authService.logout();
  /// await delegate.clearAndNavigateTo(AppRoutePath.home());
  ///
  /// // 完成新手引导后进入主界面
  /// await delegate.clearAndNavigateTo(AppRoutePath.home());
  /// ```
  Future<void> clearAndNavigateTo(AppRoutePath route) async {
    _pages.clear();
    await _navigateToRoute(route);
  }

  /// 内部导航方法
  Future<void> _navigateToRoute(AppRoutePath route) async {
    try {
      // 检查路由守卫
      final guardResponse = await _guardManager.canActivate(route);
      
      if (guardResponse.shouldRedirect) {
        // 需要重定向
        final redirectUri = Uri.parse(guardResponse.redirectTo!);
        final redirectRoute = AppRoutePath.fromUri(redirectUri);
        await _navigateToRoute(redirectRoute);
        return;
      }
      
      if (guardResponse.isDenied) {
        // 访问被拒绝，显示错误或保持当前页面
        debugPrint('路由访问被拒绝: ${guardResponse.reason}');
        _showAccessDeniedSnackBar(guardResponse.reason);
        return;
      }

      // 创建页面
      final page = _createPage(route);
      if (page != null) {
        // 检查是否是替换当前页面还是添加新页面
        if (_shouldReplacePage(route)) {
          if (_pages.isNotEmpty) {
            _pages.removeLast();
          }
        }
        
        _pages.add(page);
        _currentConfiguration = route;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('导航失败: $e');
      _showErrorSnackBar('导航失败: $e');
    }
  }

  /// 创建页面
  Page? _createPage(AppRoutePath route) {
    switch (route.location) {
      case AppRoutes.home:
        return const MaterialPage(
          key: ValueKey('home'),
          child: HomePage(),
        );
      
      case AppRoutes.detail:
        return const MaterialPage(
          key: ValueKey('detail'),
          child: DetailPage(),
        );
      
      case AppRoutes.login:
        final redirectTo = route.queryParameters['redirect'];
        return MaterialPage(
          key: const ValueKey('login'),
          child: LoginPage(redirectTo: redirectTo),
        );
      
      case AppRoutes.notFound:
        return const MaterialPage(
          key: ValueKey('notFound'),
          child: NotFoundPage(),
        );
      
      default:
        return const MaterialPage(
          key: ValueKey('notFound'),
          child: NotFoundPage(),
        );
    }
  }

  /// 判断是否应该替换当前页面
  bool _shouldReplacePage(AppRoutePath route) {
    // 登录页面总是替换当前页面
    if (route.location == AppRoutes.login) {
      return true;
    }
    
    // 如果当前页面是登录页面，新页面替换它
    if (_currentConfiguration.location == AppRoutes.login) {
      return true;
    }
    
    // 404页面总是替换当前页面
    if (route.location == AppRoutes.notFound) {
      return true;
    }
    
    return false;
  }

  /// 处理页面移除
  void _onDidRemovePage(Page<Object?> page) {
    if (_pages.contains(page)) {
      _pages.remove(page);
      _updateCurrentConfiguration();
      notifyListeners();
    }
  }

  /// 更新当前配置
  void _updateCurrentConfiguration() {
    if (_pages.isNotEmpty) {
      final lastPage = _pages.last;
      final key = lastPage.key;

      if (key == const ValueKey('home')) {
        _currentConfiguration = AppRoutePath.home();
      } else if (key == const ValueKey('detail')) {
        _currentConfiguration = AppRoutePath.detail();
      } else if (key == const ValueKey('login')) {
        _currentConfiguration = AppRoutePath.login();
      } else if (key == const ValueKey('notFound')) {
        _currentConfiguration = AppRoutePath.notFound();
      } else {
        _currentConfiguration = AppRoutePath.home();
      }
    } else {
      _currentConfiguration = AppRoutePath.home();
    }
  }

  /// 认证状态变化处理
  void _onAuthStateChanged() {
    // 如果用户登出且当前在受保护的页面，重定向到首页
    if (!_authService.isAuthenticated && _currentConfiguration.isProtected) {
      clearAndNavigateTo(AppRoutePath.home());
    }
  }

  /// 显示访问被拒绝的提示
  void _showAccessDeniedSnackBar(String? reason) {
    final context = _navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(reason ?? '访问被拒绝'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    final context = _navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _authService.removeListener(_onAuthStateChanged);
    super.dispose();
  }
}
