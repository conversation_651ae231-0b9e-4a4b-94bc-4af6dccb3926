import 'package:flutter/foundation.dart';
import '../services/auth_service.dart';
import 'app_routes.dart';

/// 路由守卫响应类
///
/// 用于表示路由守卫的检查结果，包含是否允许访问、重定向地址和拒绝原因
///
/// 使用示例：
/// ```dart
/// // 允许访问
/// return GuardResponse.allow();
///
/// // 拒绝访问并提供原因
/// return GuardResponse.deny(reason: '权限不足');
///
/// // 重定向到登录页
/// return GuardResponse.redirect('/login', reason: '请先登录');
/// ```
class GuardResponse {
  /// 是否允许激活路由
  /// true: 允许访问目标路由
  /// false: 拒绝访问或需要重定向
  final bool canActivate;

  /// 重定向目标路径
  /// 当 canActivate 为 false 且此字段不为空时，表示需要重定向到指定路径
  /// 例如：'/login', '/unauthorized' 等
  final String? redirectTo;

  /// 拒绝访问或重定向的原因
  /// 用于向用户显示友好的提示信息
  /// 例如：'请先登录', '权限不足', '页面不存在' 等
  final String? reason;

  /// 创建路由守卫响应
  ///
  /// 参数说明：
  /// - [canActivate]: 是否允许访问，必填
  /// - [redirectTo]: 重定向路径，可选，当需要重定向时填写
  /// - [reason]: 拒绝原因，可选，用于用户提示
  const GuardResponse({
    required this.canActivate,
    this.redirectTo,
    this.reason,
  });

  /// 创建允许访问的响应
  ///
  /// 使用场景：用户有权限访问目标路由
  ///
  /// 示例：
  /// ```dart
  /// if (user.hasPermission('read')) {
  ///   return GuardResponse.allow();
  /// }
  /// ```
  factory GuardResponse.allow() => const GuardResponse(canActivate: true);

  /// 创建拒绝访问的响应
  ///
  /// 参数说明：
  /// - [reason]: 拒绝原因，可选，默认为 '访问被拒绝'
  ///
  /// 使用场景：用户无权限访问，且不需要重定向
  ///
  /// 示例：
  /// ```dart
  /// if (!user.hasRole('admin')) {
  ///   return GuardResponse.deny(reason: '需要管理员权限');
  /// }
  /// ```
  factory GuardResponse.deny({String? reason}) {
    return GuardResponse(
      canActivate: false,
      reason: reason ?? '访问被拒绝',
    );
  }

  /// 创建重定向响应
  ///
  /// 参数说明：
  /// - [redirectTo]: 重定向目标路径，必填
  /// - [reason]: 重定向原因，可选，用于用户提示
  ///
  /// 使用场景：需要将用户重定向到其他页面
  ///
  /// 示例：
  /// ```dart
  /// if (!authService.isAuthenticated) {
  ///   return GuardResponse.redirect('/login', reason: '请先登录');
  /// }
  /// ```
  factory GuardResponse.redirect(String redirectTo, {String? reason}) {
    return GuardResponse(
      canActivate: false,
      redirectTo: redirectTo,
      reason: reason,
    );
  }

  /// 是否应该重定向
  ///
  /// 返回 true 当：
  /// - canActivate 为 false
  /// - redirectTo 不为空
  bool get shouldRedirect => redirectTo != null;

  /// 是否被拒绝访问
  ///
  /// 返回 true 当：
  /// - canActivate 为 false
  /// - redirectTo 为空（不需要重定向，直接拒绝）
  bool get isDenied => !canActivate && redirectTo == null;
}

/// 路由守卫接口
///
/// 所有路由守卫都必须实现此接口
///
/// 实现示例：
/// ```dart
/// class CustomGuard implements RouteGuard {
///   @override
///   int get priority => 50; // 优先级，数字越小越先执行
///
///   @override
///   Future<GuardResponse> canActivate(AppRoutePath route) async {
///     // 自定义验证逻辑
///     if (someCondition) {
///       return GuardResponse.allow();
///     } else {
///       return GuardResponse.deny(reason: '自定义拒绝原因');
///     }
///   }
/// }
/// ```
abstract class RouteGuard {
  /// 检查是否可以激活路由
  ///
  /// 参数说明：
  /// - [route]: 要访问的路由路径对象，包含路径、查询参数等信息
  ///
  /// 返回值：
  /// - [GuardResponse]: 守卫检查结果
  ///   - allow(): 允许访问
  ///   - deny(): 拒绝访问
  ///   - redirect(): 重定向到其他路径
  ///
  /// 使用场景：
  /// - 检查用户是否已登录
  /// - 验证用户权限
  /// - 检查路由参数有效性
  /// - 实现自定义访问控制逻辑
  Future<GuardResponse> canActivate(AppRoutePath route);

  /// 守卫优先级（数字越小优先级越高）
  ///
  /// 优先级说明：
  /// - 1-10: 系统级守卫（如认证守卫）
  /// - 11-50: 业务级守卫（如权限守卫）
  /// - 51-100: 自定义守卫
  /// - 100+: 低优先级守卫
  ///
  /// 默认值：100
  int get priority => 100;
}

/// 认证守卫
///
/// 用于检查用户是否已登录，保护需要认证的路由
///
/// 功能特性：
/// - 自动识别公开路由和受保护路由
/// - 未登录用户自动重定向到登录页
/// - 支持登录后重定向回原页面
///
/// 使用示例：
/// ```dart
/// final guardManager = RouteGuardManager();
/// guardManager.addGlobalGuard(AuthGuard());
/// ```
class AuthGuard implements RouteGuard {
  /// 认证服务实例
  /// 用于检查用户登录状态
  final AuthService _authService = AuthService();

  /// 认证守卫优先级：10（高优先级）
  /// 认证检查应该在其他业务逻辑之前执行
  @override
  int get priority => 10;

  /// 检查路由是否可以激活
  ///
  /// 检查逻辑：
  /// 1. 如果是公开路由（如首页、登录页），直接允许访问
  /// 2. 如果是受保护路由，检查用户是否已登录
  /// 3. 未登录用户重定向到登录页，并保存原始访问路径
  ///
  /// 参数说明：
  /// - [route]: 要访问的路由对象
  ///
  /// 返回值：
  /// - 允许访问：用户已登录或访问公开路由
  /// - 重定向：未登录用户访问受保护路由，重定向到登录页
  @override
  Future<GuardResponse> canActivate(AppRoutePath route) async {
    // 如果是公开路由，直接允许访问
    // 公开路由包括：首页、登录页、404页面等
    if (route.isPublic) {
      return GuardResponse.allow();
    }

    // 检查用户是否已认证
    if (!_authService.isAuthenticated) {
      // 未认证用户重定向到登录页
      final loginPath = AppRoutes.login;

      // 如果不是首页，添加重定向参数，登录后可以回到原页面
      final redirectQuery = route.location != AppRoutes.home
          ? '?redirect=${Uri.encodeComponent(route.fullPath)}'
          : '';

      return GuardResponse.redirect(
        '$loginPath$redirectQuery',
        reason: '请先登录以访问此页面',
      );
    }

    // 用户已认证，允许访问
    return GuardResponse.allow();
  }
}

/// 角色守卫
///
/// 基于用户角色的访问控制守卫
///
/// 功能特性：
/// - 支持多角色验证（用户只需拥有其中一个角色即可）
/// - 自动检查用户登录状态
/// - 提供友好的权限不足提示
///
/// 使用示例：
/// ```dart
/// // 要求用户具有管理员角色
/// final adminGuard = RoleGuard(requiredRoles: ['admin']);
///
/// // 要求用户具有管理员或编辑者角色之一
/// final editorGuard = RoleGuard(requiredRoles: ['admin', 'editor']);
///
/// // 为特定路由添加角色守卫
/// guardManager.addRouteGuard('/admin', adminGuard);
/// ```
class RoleGuard implements RouteGuard {
  /// 必需的角色列表
  /// 用户只需拥有列表中的任意一个角色即可通过验证
  ///
  /// 示例：
  /// - ['admin']: 只有管理员可以访问
  /// - ['admin', 'editor']: 管理员或编辑者可以访问
  /// - ['user', 'vip']: 普通用户或VIP用户可以访问
  final List<String> requiredRoles;

  /// 认证服务实例
  /// 用于获取当前用户信息和角色
  final AuthService _authService = AuthService();

  /// 创建角色守卫
  ///
  /// 参数说明：
  /// - [requiredRoles]: 必需的角色列表，不能为空
  ///
  /// 使用示例：
  /// ```dart
  /// // 单角色验证
  /// final guard = RoleGuard(requiredRoles: ['admin']);
  ///
  /// // 多角色验证（满足其中一个即可）
  /// final guard = RoleGuard(requiredRoles: ['admin', 'moderator']);
  /// ```
  RoleGuard({required this.requiredRoles});

  /// 角色守卫优先级：20（中等优先级）
  /// 在认证守卫之后，自定义守卫之前执行
  @override
  int get priority => 20;

  /// 检查用户角色是否满足要求
  ///
  /// 检查逻辑：
  /// 1. 如果没有指定角色要求，直接允许访问
  /// 2. 检查用户是否已登录，未登录则重定向到登录页
  /// 3. 检查用户是否拥有所需角色，无权限则拒绝访问
  ///
  /// 参数说明：
  /// - [route]: 要访问的路由对象
  ///
  /// 返回值：
  /// - 允许访问：用户拥有所需角色
  /// - 重定向：用户未登录，重定向到登录页
  /// - 拒绝访问：用户已登录但无所需角色
  @override
  Future<GuardResponse> canActivate(AppRoutePath route) async {
    // 如果没有指定角色要求，直接允许
    if (requiredRoles.isEmpty) {
      return GuardResponse.allow();
    }

    // 检查用户是否已认证
    if (!_authService.isAuthenticated) {
      return GuardResponse.redirect(
        AppRoutes.login,
        reason: '请先登录',
      );
    }

    // 检查用户角色
    final user = _authService.currentUser;
    if (user == null || !user.hasAnyRole(requiredRoles)) {
      return GuardResponse.deny(
        reason: '您没有访问此页面的权限，需要以下角色之一：${requiredRoles.join(', ')}',
      );
    }

    return GuardResponse.allow();
  }
}

/// 自定义守卫
///
/// 允许开发者创建自定义的路由守卫逻辑
///
/// 功能特性：
/// - 支持自定义验证函数
/// - 可配置优先级
/// - 灵活的业务逻辑实现
///
/// 使用示例：
/// ```dart
/// // 创建时间限制守卫
/// final timeGuard = CustomGuard(
///   priority: 30,
///   guardFunction: (route) async {
///     final now = DateTime.now();
///     final hour = now.hour;
///
///     // 只允许工作时间访问
///     if (hour >= 9 && hour <= 18) {
///       return GuardResponse.allow();
///     } else {
///       return GuardResponse.deny(reason: '仅在工作时间（9:00-18:00）可访问');
///     }
///   },
/// );
///
/// // 创建IP地址验证守卫
/// final ipGuard = CustomGuard(
///   guardFunction: (route) async {
///     final userIP = await getCurrentUserIP();
///     if (allowedIPs.contains(userIP)) {
///       return GuardResponse.allow();
///     } else {
///       return GuardResponse.deny(reason: 'IP地址不在允许列表中');
///     }
///   },
/// );
/// ```
class CustomGuard implements RouteGuard {
  /// 自定义守卫验证函数
  ///
  /// 函数签名：
  /// - 参数：[AppRoutePath route] 要访问的路由
  /// - 返回值：[Future<GuardResponse>] 守卫检查结果
  ///
  /// 实现要求：
  /// - 必须返回 GuardResponse 对象
  /// - 可以是异步函数（支持网络请求、数据库查询等）
  /// - 应该处理可能的异常情况
  final Future<GuardResponse> Function(AppRoutePath route) _guardFunction;

  /// 守卫优先级
  /// 数字越小优先级越高，默认为100
  final int _priority;

  /// 创建自定义守卫
  ///
  /// 参数说明：
  /// - [guardFunction]: 自定义验证函数，必填
  /// - [priority]: 守卫优先级，可选，默认为100
  ///
  /// 使用示例：
  /// ```dart
  /// final guard = CustomGuard(
  ///   priority: 50, // 中等优先级
  ///   guardFunction: (route) async {
  ///     // 自定义验证逻辑
  ///     if (await customValidation(route)) {
  ///       return GuardResponse.allow();
  ///     } else {
  ///       return GuardResponse.deny(reason: '自定义验证失败');
  ///     }
  ///   },
  /// );
  /// ```
  CustomGuard({
    required Future<GuardResponse> Function(AppRoutePath route) guardFunction,
    int priority = 100,
  }) : _guardFunction = guardFunction, _priority = priority;

  @override
  int get priority => _priority;

  @override
  Future<GuardResponse> canActivate(AppRoutePath route) {
    return _guardFunction(route);
  }
}

/// 路由守卫管理器
///
/// 负责管理所有路由守卫，包括全局守卫和特定路由守卫
///
/// 功能特性：
/// - 支持全局守卫和路由特定守卫
/// - 自动按优先级排序执行
/// - 提供守卫的增删改查功能
/// - 支持守卫信息调试
///
/// 使用示例：
/// ```dart
/// final manager = RouteGuardManager();
///
/// // 添加全局守卫（对所有路由生效）
/// manager.addGlobalGuard(AuthGuard());
/// manager.addGlobalGuard(RoleGuard(requiredRoles: ['user']));
///
/// // 添加特定路由守卫（只对指定路由生效）
/// manager.addRouteGuard('/admin', RoleGuard(requiredRoles: ['admin']));
/// manager.addRouteGuard('/vip', CustomGuard(
///   guardFunction: (route) async => checkVipStatus(),
/// ));
///
/// // 检查路由是否可以访问
/// final response = await manager.canActivate(route);
/// if (response.canActivate) {
///   // 允许访问
/// } else if (response.shouldRedirect) {
///   // 需要重定向
/// } else {
///   // 拒绝访问
/// }
/// ```
class RouteGuardManager {
  /// 全局守卫列表
  /// 对所有路由都会执行的守卫
  final List<RouteGuard> _guards = [];

  /// 路由特定守卫映射
  /// key: 路由路径（如 '/admin', '/user/profile'）
  /// value: 该路由的守卫列表
  final Map<String, List<RouteGuard>> _routeSpecificGuards = {};

  /// 添加全局守卫
  ///
  /// 全局守卫会对所有路由生效，通常用于：
  /// - 认证检查
  /// - 基础权限验证
  /// - 系统级别的访问控制
  ///
  /// 参数说明：
  /// - [guard]: 要添加的守卫实例
  ///
  /// 使用示例：
  /// ```dart
  /// // 添加认证守卫
  /// manager.addGlobalGuard(AuthGuard());
  ///
  /// // 添加基础角色守卫
  /// manager.addGlobalGuard(RoleGuard(requiredRoles: ['user']));
  /// ```
  void addGlobalGuard(RouteGuard guard) {
    _guards.add(guard);
    _sortGuards();
  }

  /// 为特定路由添加守卫
  ///
  /// 路由特定守卫只对指定路由生效，通常用于：
  /// - 特殊页面的权限控制
  /// - 业务相关的访问限制
  /// - 页面级别的自定义验证
  ///
  /// 参数说明：
  /// - [route]: 路由路径，如 '/admin', '/user/profile'
  /// - [guard]: 要添加的守卫实例
  ///
  /// 使用示例：
  /// ```dart
  /// // 为管理员页面添加管理员权限守卫
  /// manager.addRouteGuard('/admin', RoleGuard(requiredRoles: ['admin']));
  ///
  /// // 为VIP页面添加自定义守卫
  /// manager.addRouteGuard('/vip', CustomGuard(
  ///   guardFunction: (route) async {
  ///     return await checkVipMembership()
  ///       ? GuardResponse.allow()
  ///       : GuardResponse.deny(reason: '需要VIP会员资格');
  ///   },
  /// ));
  /// ```
  void addRouteGuard(String route, RouteGuard guard) {
    _routeSpecificGuards.putIfAbsent(route, () => []).add(guard);
    _sortRouteGuards(route);
  }

  /// 移除守卫
  ///
  /// 从全局守卫和所有路由特定守卫中移除指定守卫
  ///
  /// 参数说明：
  /// - [guard]: 要移除的守卫实例
  ///
  /// 使用示例：
  /// ```dart
  /// final authGuard = AuthGuard();
  /// manager.addGlobalGuard(authGuard);
  ///
  /// // 稍后移除
  /// manager.removeGuard(authGuard);
  /// ```
  void removeGuard(RouteGuard guard) {
    _guards.remove(guard);
    for (final guards in _routeSpecificGuards.values) {
      guards.remove(guard);
    }
  }

  /// 清除所有守卫
  ///
  /// 移除所有全局守卫和路由特定守卫
  /// 通常用于：
  /// - 应用重置
  /// - 测试环境清理
  /// - 守卫配置重新初始化
  ///
  /// 使用示例：
  /// ```dart
  /// // 清除所有守卫
  /// manager.clearGuards();
  ///
  /// // 重新配置守卫
  /// manager.initializeDefaultGuards();
  /// ```
  void clearGuards() {
    _guards.clear();
    _routeSpecificGuards.clear();
  }

  /// 检查路由是否可以激活
  ///
  /// 这是守卫管理器的核心方法，负责执行所有相关守卫的检查
  ///
  /// 执行流程：
  /// 1. 收集所有适用的守卫（全局守卫 + 路由特定守卫）
  /// 2. 按优先级排序（数字越小优先级越高）
  /// 3. 依次执行每个守卫的 canActivate 方法
  /// 4. 如果任何守卫拒绝访问或要求重定向，立即返回结果
  /// 5. 如果所有守卫都通过，返回允许访问
  ///
  /// 参数说明：
  /// - [route]: 要检查的路由路径对象
  ///
  /// 返回值：
  /// - [GuardResponse]: 综合所有守卫检查的最终结果
  ///   - allow(): 所有守卫都通过，允许访问
  ///   - deny(): 某个守卫拒绝访问
  ///   - redirect(): 某个守卫要求重定向
  ///
  /// 异常处理：
  /// - 如果某个守卫执行时抛出异常，会被捕获并返回拒绝访问的结果
  /// - 异常信息会通过 debugPrint 输出到控制台
  ///
  /// 使用示例：
  /// ```dart
  /// final response = await manager.canActivate(AppRoutePath.detail());
  ///
  /// if (response.canActivate) {
  ///   // 允许访问，继续导航
  ///   navigateToRoute(route);
  /// } else if (response.shouldRedirect) {
  ///   // 需要重定向
  ///   navigateToRoute(AppRoutePath.fromUri(Uri.parse(response.redirectTo!)));
  /// } else {
  ///   // 拒绝访问，显示错误信息
  ///   showErrorMessage(response.reason ?? '访问被拒绝');
  /// }
  /// ```
  Future<GuardResponse> canActivate(AppRoutePath route) async {
    // 获取所有适用的守卫
    // 包括全局守卫和当前路由的特定守卫
    final allGuards = <RouteGuard>[
      ..._guards, // 全局守卫
      ...(_routeSpecificGuards[route.location] ?? []), // 路由特定守卫
    ];

    // 按优先级排序（数字越小优先级越高）
    // 确保重要的守卫（如认证守卫）优先执行
    allGuards.sort((a, b) => a.priority.compareTo(b.priority));

    // 依次执行守卫检查
    for (final guard in allGuards) {
      try {
        final response = await guard.canActivate(route);

        // 如果守卫拒绝访问或要求重定向，立即返回
        // 不再执行后续守卫，遵循"一票否决"原则
        if (!response.canActivate) {
          debugPrint('路由守卫 ${guard.runtimeType} 拒绝访问: ${response.reason}');
          return response;
        }
      } catch (e) {
        // 捕获守卫执行异常，防止应用崩溃
        debugPrint('路由守卫 ${guard.runtimeType} 执行失败: $e');
        return GuardResponse.deny(reason: '路由守卫执行失败');
      }
    }

    // 所有守卫都通过，允许访问
    return GuardResponse.allow();
  }

  /// 初始化默认守卫
  ///
  /// 设置应用的基础守卫配置，通常在应用启动时调用
  ///
  /// 默认配置包括：
  /// - 认证守卫：保护需要登录的页面
  /// - 可根据需要添加其他默认守卫
  ///
  /// 使用示例：
  /// ```dart
  /// final manager = RouteGuardManager();
  /// manager.initializeDefaultGuards(); // 设置默认守卫
  ///
  /// // 可以在此基础上添加更多守卫
  /// manager.addRouteGuard('/admin', RoleGuard(requiredRoles: ['admin']));
  /// ```
  void initializeDefaultGuards() {
    // 添加认证守卫（全局生效）
    // 确保用户在访问受保护页面时已登录
    addGlobalGuard(AuthGuard());

    // 示例：为详情页添加特定的角色守卫
    // 取消注释以启用基础用户角色验证
    // addRouteGuard(AppRoutes.detail, RoleGuard(requiredRoles: ['user']));

    // 示例：添加更多默认守卫
    // addGlobalGuard(CustomGuard(
    //   priority: 5,
    //   guardFunction: (route) async {
    //     // 检查应用是否在维护模式
    //     if (await isMaintenanceMode()) {
    //       return GuardResponse.redirect('/maintenance');
    //     }
    //     return GuardResponse.allow();
    //   },
    // ));
  }

  /// 排序全局守卫
  ///
  /// 内部方法，按优先级对全局守卫进行排序
  /// 确保高优先级守卫优先执行
  void _sortGuards() {
    _guards.sort((a, b) => a.priority.compareTo(b.priority));
  }

  /// 排序特定路由的守卫
  ///
  /// 内部方法，按优先级对指定路由的守卫进行排序
  ///
  /// 参数说明：
  /// - [route]: 要排序守卫的路由路径
  void _sortRouteGuards(String route) {
    final guards = _routeSpecificGuards[route];
    if (guards != null) {
      guards.sort((a, b) => a.priority.compareTo(b.priority));
    }
  }

  /// 获取所有守卫信息（用于调试）
  ///
  /// 返回当前管理器中所有守卫的详细信息，便于调试和监控
  ///
  /// 返回值：
  /// - Map 包含以下信息：
  ///   - 'globalGuards': 全局守卫类型列表
  ///   - 'routeSpecificGuards': 路由特定守卫映射
  ///
  /// 使用示例：
  /// ```dart
  /// final info = manager.getGuardsInfo();
  /// print('全局守卫: ${info['globalGuards']}');
  /// print('路由守卫: ${info['routeSpecificGuards']}');
  ///
  /// // 输出示例：
  /// // 全局守卫: [AuthGuard, CustomGuard]
  /// // 路由守卫: {/admin: [RoleGuard], /vip: [CustomGuard]}
  /// ```
  Map<String, dynamic> getGuardsInfo() {
    return {
      'globalGuards': _guards.map((g) => g.runtimeType.toString()).toList(),
      'routeSpecificGuards': _routeSpecificGuards.map(
        (route, guards) => MapEntry(
          route,
          guards.map((g) => g.runtimeType.toString()).toList(),
        ),
      ),
    };
  }
}
