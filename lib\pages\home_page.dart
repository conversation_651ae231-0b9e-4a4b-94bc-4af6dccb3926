import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../router/app_routes.dart';
import '../router/app_router_delegate.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final AuthService _authService = AuthService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Move App Pro'),
        actions: [
          if (_authService.isAuthenticated)
            PopupMenuButton<String>(
              onSelected: (value) async {
                switch (value) {
                  case 'profile':
                    _showUserProfile();
                    break;
                  case 'logout':
                    await _handleLogout();
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'profile',
                  child: Row(
                    children: [
                      const Icon(Icons.person),
                      const SizedBox(width: 8),
                      Text(_authService.currentUser?.username ?? 'User'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'logout',
                  child: Row(
                    children: [
                      Icon(Icons.logout),
                      SizedBox(width: 8),
                      Text('退出登录'),
                    ],
                  ),
                ),
              ],
            )
          else
            TextButton(
              onPressed: () => _navigateToLogin(),
              child: const Text('登录'),
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeCard(),
            const SizedBox(height: 20),
            _buildQuickActions(),
            const SizedBox(height: 20),
            _buildFeaturesList(),
          ],
        ),
      ),
      floatingActionButton: _authService.isAuthenticated
          ? FloatingActionButton(
              onPressed: () => _navigateToDetail(),
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _authService.isAuthenticated
                  ? '欢迎回来, ${_authService.currentUser?.username ?? 'User'}!'
                  : '欢迎使用 Move App Pro',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _authService.isAuthenticated
                  ? '您已成功登录，可以访问所有功能。'
                  : '请登录以访问完整功能。',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快速操作',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                icon: Icons.info,
                title: '详情页面',
                subtitle: '查看详细信息',
                onTap: () => _navigateToDetail(),
                enabled: _authService.isAuthenticated,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                icon: Icons.login,
                title: _authService.isAuthenticated ? '用户信息' : '登录',
                subtitle: _authService.isAuthenticated ? '查看个人信息' : '访问您的账户',
                onTap: _authService.isAuthenticated ? _showUserProfile : _navigateToLogin,
                enabled: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required bool enabled,
  }) {
    return Card(
      child: InkWell(
        onTap: enabled ? onTap : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: enabled ? Colors.blue : Colors.grey,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: enabled ? null : Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: enabled ? null : Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturesList() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '应用特性',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView(
              children: [
                _buildFeatureItem(
                  icon: Icons.security,
                  title: '安全认证',
                  description: '基于角色的访问控制和安全认证系统',
                ),
                _buildFeatureItem(
                  icon: Icons.navigation,
                  title: '智能路由',
                  description: '现代化的路由系统，支持深度链接和路由守卫',
                ),
                _buildFeatureItem(
                  icon: Icons.design_services,
                  title: '现代设计',
                  description: '采用 Material 3 设计语言，提供优秀的用户体验',
                ),
                _buildFeatureItem(
                  icon: Icons.speed,
                  title: '高性能',
                  description: '优化的代码结构，确保应用流畅运行',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: Colors.blue),
        title: Text(title),
        subtitle: Text(description),
      ),
    );
  }

  void _navigateToDetail() {
    if (_authService.isAuthenticated) {
      // 使用 Router 2.0 的导航方式
      final delegate = Router.of(context).routerDelegate as AppRouterDelegate;
      delegate.navigateToDetail();
    } else {
      _showLoginRequiredDialog();
    }
  }

  void _navigateToLogin() {
    // 使用 Router 2.0 的导航方式
    final delegate = Router.of(context).routerDelegate as AppRouterDelegate;
    delegate.navigateToLogin();
  }

  void _showUserProfile() {
    final user = _authService.currentUser;
    if (user != null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('用户信息'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('用户名: ${user.username}'),
              Text('邮箱: ${user.email}'),
              Text('角色: ${user.roles.join(', ')}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        ),
      );
    }
  }

  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要登录'),
        content: const Text('请先登录以访问此功能。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToLogin();
            },
            child: const Text('登录'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleLogout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('您确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('退出'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _authService.logout();
      if (mounted) {
        setState(() {});
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('已成功退出登录')),
        );
      }
    }
  }
}
