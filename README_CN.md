# Move App Pro - 中文开发文档

## 📋 项目概述

Move App Pro 是一个基于 Flutter 的现代化移动应用，采用最新的 Flutter Router 2.0 架构，实现了完整的用户认证、路由守卫和权限管理系统。

### 🎯 核心特性

- **现代化架构**: 使用 Flutter Router 2.0 实现声明式路由
- **完整认证系统**: 支持用户登录、登出、权限验证
- **路由守卫机制**: 基于角色的访问控制
- **Material 3 设计**: 采用最新的 Material Design 3 规范
- **中文注解**: 所有代码都有详细的中文注释和参数说明
- **单元测试**: 完整的测试覆盖

## 🏗️ 项目结构

```
lib/
├── main.dart                    # 应用入口文件
├── pages/                       # 页面组件
│   ├── home_page.dart          # 首页
│   ├── detail_page.dart        # 详情页
│   ├── login_page.dart         # 登录页
│   └── not_found_page.dart     # 404页面
├── router/                      # 路由系统
│   ├── app_routes.dart         # 路由常量和配置
│   ├── app_router_delegate.dart # 路由委托
│   ├── app_route_information_parser.dart # 路由解析器
│   └── route_guards.dart       # 路由守卫
└── services/                    # 服务层
    └── auth_service.dart       # 认证服务
```

## 🚀 快速开始

### 环境要求

- Flutter SDK 3.8.1 或更高版本
- Dart SDK 3.0 或更高版本

### 安装依赖

```bash
flutter pub get
```

### 运行应用

```bash
# 调试模式
flutter run

# 发布模式
flutter run --release
```

### 运行测试

```bash
# 运行所有测试
flutter test

# 生成覆盖率报告
flutter test --coverage
```

## 📚 核心组件详解

### 1. 用户认证系统 (AuthService)

#### 基本用法

```dart
// 获取认证服务实例（单例模式）
final authService = AuthService();

// 监听认证状态变化
authService.addListener(() {
  if (authService.isAuthenticated) {
    print('用户已登录: ${authService.currentUser?.username}');
  } else {
    print('用户未登录');
  }
});

// 初始化认证服务
await authService.initialize();

// 执行登录
final success = await authService.login('username', 'password');
if (success) {
  print('登录成功');
} else {
  print('登录失败');
}

// 检查用户权限
if (authService.hasRole('admin')) {
  print('用户是管理员');
}

// 登出
await authService.logout();
```

#### 演示账户

应用内置了以下演示账户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | password | admin, user | 管理员账户，拥有所有权限 |
| user | password | user | 普通用户账户 |

#### 主要方法

- `initialize()`: 初始化认证服务
- `login(username, password)`: 用户登录
- `logout()`: 用户登出
- `hasRole(role)`: 检查单个角色
- `hasAnyRole(roles)`: 检查多个角色
- `refreshToken()`: 刷新认证令牌

### 2. 路由系统

#### 路由常量 (AppRoutes)

```dart
class AppRoutes {
  static const String home = '/';          // 首页
  static const String detail = '/detail';  // 详情页
  static const String login = '/login';    // 登录页
  static const String notFound = '/404';   // 404页面
}
```

#### 路由对象 (AppRoutePath)

```dart
// 创建简单路由
final homeRoute = AppRoutePath.home();

// 创建带查询参数的路由
final detailRoute = AppRoutePath.detail(
  queryParams: {'id': '123', 'tab': 'info'}
);

// 创建登录路由并指定重定向
final loginRoute = AppRoutePath.login(redirectTo: '/detail');

// 从URI创建路由
final route = AppRoutePath.fromUri(Uri.parse('/detail?id=123'));

// 获取完整路径
print(route.fullPath); // 输出: /detail?id=123

// 检查路由权限
if (route.isProtected) {
  print('这是受保护的路由');
}
```

#### 程序化导航

```dart
// 获取路由委托
final delegate = AppRouterDelegate();

// 导航到首页
await delegate.navigateToHome();

// 导航到详情页
await delegate.navigateToDetail();

// 导航到登录页
await delegate.navigateToLogin(redirectTo: '/detail');

// 返回上一页
delegate.goBack();

// 清除历史并导航
await delegate.clearAndNavigateTo(AppRoutePath.home());
```

### 3. 路由守卫系统

#### 认证守卫 (AuthGuard)

自动检查用户是否已登录：

```dart
final authGuard = AuthGuard();
final response = await authGuard.canActivate(route);

if (response.canActivate) {
  // 允许访问
} else if (response.shouldRedirect) {
  // 需要重定向到登录页
  print('重定向到: ${response.redirectTo}');
} else {
  // 拒绝访问
  print('拒绝原因: ${response.reason}');
}
```

#### 角色守卫 (RoleGuard)

基于用户角色的访问控制：

```dart
// 要求管理员权限
final adminGuard = RoleGuard(requiredRoles: ['admin']);

// 要求管理员或编辑者权限
final editorGuard = RoleGuard(requiredRoles: ['admin', 'editor']);

// 检查权限
final response = await adminGuard.canActivate(route);
```

#### 自定义守卫 (CustomGuard)

创建自定义验证逻辑：

```dart
// 时间限制守卫
final timeGuard = CustomGuard(
  priority: 30,
  guardFunction: (route) async {
    final hour = DateTime.now().hour;
    if (hour >= 9 && hour <= 18) {
      return GuardResponse.allow();
    } else {
      return GuardResponse.deny(reason: '仅在工作时间（9:00-18:00）可访问');
    }
  },
);

// IP地址验证守卫
final ipGuard = CustomGuard(
  guardFunction: (route) async {
    final userIP = await getCurrentUserIP();
    if (allowedIPs.contains(userIP)) {
      return GuardResponse.allow();
    } else {
      return GuardResponse.deny(reason: 'IP地址不在允许列表中');
    }
  },
);
```

#### 守卫管理器 (RouteGuardManager)

```dart
final manager = RouteGuardManager();

// 添加全局守卫（对所有路由生效）
manager.addGlobalGuard(AuthGuard());
manager.addGlobalGuard(RoleGuard(requiredRoles: ['user']));

// 添加特定路由守卫（只对指定路由生效）
manager.addRouteGuard('/admin', RoleGuard(requiredRoles: ['admin']));
manager.addRouteGuard('/vip', CustomGuard(
  guardFunction: (route) async => checkVipStatus(),
));

// 检查路由是否可以访问
final response = await manager.canActivate(route);

// 获取守卫信息（调试用）
final info = manager.getGuardsInfo();
print('全局守卫: ${info['globalGuards']}');
print('路由守卫: ${info['routeSpecificGuards']}');
```

## 🎨 UI 组件

### 主题配置

应用使用 Material 3 设计系统：

```dart
ThemeData(
  primarySwatch: Colors.blue,
  useMaterial3: true,
  appBarTheme: const AppBarTheme(
    centerTitle: true,
    elevation: 0,
  ),
  cardTheme: CardThemeData(
    elevation: 2,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
  ),
)
```

### 页面组件

#### 首页 (HomePage)
- 欢迎卡片
- 快速操作按钮
- 应用特性列表
- 用户状态显示

#### 详情页 (DetailPage)
- 多标签页界面（列表、网格、设置）
- 项目管理功能
- 用户设置面板

#### 登录页 (LoginPage)
- 用户名密码登录
- 演示账户快速填充
- 登录后重定向支持

## 🧪 测试

### 测试覆盖

项目包含完整的单元测试：

- ✅ 用户模型测试
- ✅ 认证服务测试
- ✅ 路由配置测试
- ✅ 路由守卫测试
- ✅ UI组件测试

### 运行特定测试

```bash
# 运行特定测试文件
flutter test test/widget_test.dart

# 运行特定测试组
flutter test --plain-name "用户模型测试"

# 详细输出
flutter test --verbose
```

## 🔧 开发指南

### 添加新页面

1. 在 `lib/pages/` 目录创建页面文件
2. 在 `AppRoutes` 中添加路由常量
3. 在 `AppRouterDelegate` 中添加页面创建逻辑
4. 根据需要配置路由守卫

### 添加新的路由守卫

1. 实现 `RouteGuard` 接口
2. 在 `RouteGuardManager` 中注册守卫
3. 配置守卫优先级

### 扩展认证功能

1. 修改 `User` 模型添加新字段
2. 更新 `AuthService` 的登录逻辑
3. 添加相应的权限检查方法

## 📖 API 参考

详细的 API 文档请参考各个文件中的中文注释，每个类和方法都有完整的参数说明和使用示例。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
